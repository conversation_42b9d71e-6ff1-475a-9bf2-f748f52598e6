<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Integration Setup Solution</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      color: #333;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      padding: 20px;
    }
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      padding: 20px;
      margin-bottom: 20px;
    }
    h1, h2 {
      color: #333;
    }
    .form-group {
      margin-bottom: 15px;
    }
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    input {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    .button-group {
      margin-top: 20px;
      display: flex;
      gap: 10px;
    }
    button {
      padding: 10px 15px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-weight: 500;
    }
    .primary-button {
      background-color: #3b82f6;
      color: white;
    }
    .secondary-button {
      background-color: #f3f4f6;
      color: #374151;
    }
    .success-button {
      background-color: #10b981;
      color: white;
    }
    .message {
      margin-top: 15px;
      padding: 10px;
      border-radius: 4px;
    }
    .success {
      background-color: #ecfdf5;
      color: #065f46;
      border: 1px solid #a7f3d0;
    }
    .error {
      background-color: #fef2f2;
      color: #991b1b;
      border: 1px solid #fecaca;
    }
    .disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    .back-link {
      color: #3b82f6;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 5px;
    }
    .back-link:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Integration Setup</h1>
      <a href="/" class="back-link">← Back to Dashboard</a>
    </div>
    
    <div class="card">
      <div id="integration-form">
        <div id="form-header">
          <h2 id="form-title">Configure Microsoft Entra ID Integration</h2>
          <p id="form-description">Configure your Microsoft Entra ID integration below to connect your tenant with Rolewise.ai.</p>
          <div id="edit-button-container" style="display: none;">
            <button id="edit-button" class="primary-button">Edit Configuration</button>
          </div>
        </div>
        
        <form id="setup-form">
          <div class="form-group">
            <label for="tenant-id">Entra ID Tenant ID:</label>
            <input type="text" id="tenant-id" placeholder="Enter your Entra ID Tenant ID">
          </div>
          
          <div class="form-group">
            <label for="client-id">Client ID:</label>
            <input type="text" id="client-id" placeholder="Enter your Client ID">
          </div>
          
          <div class="form-group">
            <label for="client-secret">Client Secret:</label>
            <input type="password" id="client-secret" placeholder="Enter your Client Secret">
          </div>
          
          <div class="button-group">
            <button type="button" id="save-button" class="primary-button">Save Integration</button>
            <button type="button" id="cancel-button" class="secondary-button" style="display: none;">Cancel</button>
            <button type="button" id="test-button" class="success-button">Test Connection</button>
          </div>
        </form>
        
        <div id="message" class="message" style="display: none;"></div>
      </div>
    </div>
  </div>

  <script>
    // State management
    const state = {
      tenantId: '',
      clientId: '',
      clientSecret: '',
      existingIntegration: false,
      editMode: false,
      loading: false,
      appTenantId: '12834c0f-768d-4a18-b2f4-a4d8e4ce29b6' // Simulated app tenant ID
    };
    
    // DOM elements
    const formTitle = document.getElementById('form-title');
    const formDescription = document.getElementById('form-description');
    const editButtonContainer = document.getElementById('edit-button-container');
    const editButton = document.getElementById('edit-button');
    const tenantIdInput = document.getElementById('tenant-id');
    const clientIdInput = document.getElementById('client-id');
    const clientSecretInput = document.getElementById('client-secret');
    const saveButton = document.getElementById('save-button');
    const cancelButton = document.getElementById('cancel-button');
    const testButton = document.getElementById('test-button');
    const messageElement = document.getElementById('message');
    
    // Check for existing integration (simulated)
    function checkExistingIntegration() {
      // Simulate API call delay
      setTimeout(() => {
        // For demo purposes, randomly decide if there's an existing integration
        // In a real app, this would be an API call to check for existing integration
        const hasExisting = localStorage.getItem('integration');
        
        if (hasExisting) {
          const integration = JSON.parse(hasExisting);
          state.existingIntegration = true;
          state.tenantId = integration.tenantId || '';
          state.clientId = integration.clientId || '';
          // Don't populate client secret for security reasons
          
          // Update UI for existing integration
          tenantIdInput.value = state.tenantId;
          clientIdInput.value = state.clientId;
          
          updateUIForExistingIntegration();
        }
      }, 500);
    }
    
    // Update UI for existing integration
    function updateUIForExistingIntegration() {
      formTitle.textContent = 'Microsoft Entra ID Integration';
      formDescription.textContent = 'Your Microsoft Entra ID integration is configured. You can update it below.';
      editButtonContainer.style.display = 'block';
      
      if (!state.editMode) {
        // Disable inputs in view mode
        tenantIdInput.disabled = true;
        clientIdInput.disabled = true;
        clientSecretInput.disabled = true;
        clientSecretInput.placeholder = 'Enter new Client Secret (leave blank to keep current)';
        
        // Hide save button in view mode
        saveButton.style.display = 'none';
        cancelButton.style.display = 'none';
      } else {
        // Enable inputs in edit mode
        tenantIdInput.disabled = false;
        clientIdInput.disabled = false;
        clientSecretInput.disabled = false;
        
        // Show save and cancel buttons in edit mode
        saveButton.textContent = 'Update Integration';
        saveButton.style.display = 'inline-block';
        cancelButton.style.display = 'inline-block';
      }
    }
    
    // Save integration
    function saveIntegration() {
      const tenantId = tenantIdInput.value.trim();
      const clientId = clientIdInput.value.trim();
      const clientSecret = clientSecretInput.value.trim();
      
      if (!tenantId || !clientId) {
        showMessage('Tenant ID and Client ID are required.', 'error');
        return;
      }
      
      if (!state.existingIntegration && !clientSecret) {
        showMessage('Client Secret is required for new integrations.', 'error');
        return;
      }
      
      // Simulate loading state
      setLoading(true);
      
      // Simulate API call
      setTimeout(() => {
        // In a real app, this would be an API call to save the integration
        const integration = {
          tenantId,
          clientId,
          appTenantId: state.appTenantId
        };
        
        // Store in localStorage for demo purposes
        localStorage.setItem('integration', JSON.stringify(integration));
        
        // Update state
        state.existingIntegration = true;
        state.tenantId = tenantId;
        state.clientId = clientId;
        state.editMode = false;
        
        // Update UI
        updateUIForExistingIntegration();
        showMessage(`Integration ${state.existingIntegration ? 'updated' : 'created'} successfully!`, 'success');
        setLoading(false);
      }, 1000);
    }
    
    // Test connection
    function testConnection() {
      const tenantId = tenantIdInput.value.trim();
      const clientId = clientIdInput.value.trim();
      
      if (!tenantId || !clientId) {
        showMessage('Tenant ID and Client ID are required for testing.', 'error');
        return;
      }
      
      // Simulate loading state
      setLoading(true);
      
      // Simulate API call
      setTimeout(() => {
        // In a real app, this would be an API call to test the connection
        const success = Math.random() > 0.3; // 70% chance of success for demo
        
        if (success) {
          showMessage('Connection test successful!', 'success');
        } else {
          showMessage('Connection test failed. Please check your credentials.', 'error');
        }
        
        setLoading(false);
      }, 1000);
    }
    
    // Show message
    function showMessage(text, type) {
      messageElement.textContent = text;
      messageElement.className = `message ${type}`;
      messageElement.style.display = 'block';
      
      // Auto-hide success messages after 5 seconds
      if (type === 'success') {
        setTimeout(() => {
          messageElement.style.display = 'none';
        }, 5000);
      }
    }
    
    // Set loading state
    function setLoading(loading) {
      state.loading = loading;
      
      // Disable inputs and buttons during loading
      tenantIdInput.disabled = loading || (state.existingIntegration && !state.editMode);
      clientIdInput.disabled = loading || (state.existingIntegration && !state.editMode);
      clientSecretInput.disabled = loading || (state.existingIntegration && !state.editMode);
      
      saveButton.disabled = loading;
      testButton.disabled = loading;
      editButton.disabled = loading;
      cancelButton.disabled = loading;
      
      // Update button text
      if (loading) {
        saveButton.textContent = state.existingIntegration ? 'Updating...' : 'Saving...';
        testButton.textContent = 'Testing...';
      } else {
        saveButton.textContent = state.existingIntegration ? 'Update Integration' : 'Save Integration';
        testButton.textContent = 'Test Connection';
      }
    }
    
    // Event listeners
    saveButton.addEventListener('click', saveIntegration);
    testButton.addEventListener('click', testConnection);
    
    editButton.addEventListener('click', () => {
      state.editMode = true;
      updateUIForExistingIntegration();
    });
    
    cancelButton.addEventListener('click', () => {
      state.editMode = false;
      
      // Reset form to saved values
      tenantIdInput.value = state.tenantId;
      clientIdInput.value = state.clientId;
      clientSecretInput.value = '';
      
      updateUIForExistingIntegration();
    });
    
    // Initialize
    checkExistingIntegration();
  </script>
</body>
</html>
