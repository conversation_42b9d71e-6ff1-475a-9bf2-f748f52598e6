{"name": "rolewise", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@azure/msal-browser": "^4.12.0", "@clerk/clerk-react": "^5.31.2", "@supabase/supabase-js": "^2.49.4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@vanilla-extract/css": "^1.17.2", "date-fns": "^4.1.0", "lodash": "^4.17.21", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.0", "reactflow": "^11.11.4", "vitest": "^3.1.3", "zustand": "^5.0.4"}, "devDependencies": {"@babel/preset-react": "^7.27.1", "@types/node": "^22.15.17", "@vanilla-extract/vite-plugin": "^5.0.2", "@vitejs/plugin-react": "^4.4.1", "jsdom": "^26.1.0", "vite": "^6.3.5"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "main": "index.js", "keywords": [], "author": "", "license": "ISC", "description": ""}