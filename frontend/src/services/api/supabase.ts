import { supabase } from '../supabaseClient';
import {
  SupabaseApplication,
  SupabaseGroup,
  SupabaseUserGroupMembership,
  SupabaseRoleApplicationMapping
} from '../../types/supabase';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  throw new Error('Supabase URL and Key must be provided in environment variables');
}

export async function storeApplications(tenantId: string, applications: SupabaseApplication[]): Promise<void> {
  for (const app of applications) {
    // Convert embedding to string if it's an array
    const embeddingString = Array.isArray(app.embedding)
      ? JSON.stringify(app.embedding)
      : app.embedding;

    const { error } = await supabase
      .from('applications')
      .upsert({
        tenant_id: tenantId,
        name: app.name,
        description: app.description || null,
        embedding: embeddingString,
      }, { onConflict: 'name' });

    if (error) {
      throw new Error(`Failed to store application ${app.name}: ${error.message}`);
    }
  }
}

export async function storeGroups(tenantId: string, groups: SupabaseGroup[]): Promise<void> {
  for (const group of groups) {
    const { error } = await supabase
      .from('security_groups')
      .upsert({
        tenant_id: tenantId,
        group_id: group.group_id,
        display_name: group.display_name,
        description: group.description,
        embedding: group.embedding,
      }, { onConflict: 'group_id' });

    if (error) {
      throw new Error(`Failed to store group ${group.group_id}: ${error.message}`);
    }
  }
}

export async function storeUserGroupMemberships(tenantId: string, memberships: SupabaseUserGroupMembership[]): Promise<void> {
  for (const membership of memberships) {
    const now = new Date().toISOString();
    const { error } = await supabase
      .from('user_group_memberships')
      .upsert({
        id: membership.id,
        tenant_id: tenantId,
        user_id: membership.user_id,
        group_id: membership.group_id,
        created_at: membership.created_at || now,
        updated_at: membership.updated_at || now
      }, {
        onConflict: 'id'
      });

    if (error) {
      throw new Error(`Failed to store user-group membership for user ${membership.user_id}: ${error.message}`);
    }
  }
}

export async function getUsersForTenant(tenantId: string): Promise<{ id: string }[]> {
  const { data, error } = await supabase
    .from('profiles')
    .select('id')
    .eq('tenant_id', tenantId);

  if (error) {
    throw new Error(`Failed to fetch users: ${error.message}`);
  }
  return data;
}

export async function getUserGroups(tenantId: string, userId: string): Promise<SupabaseGroup[]> {
  const { data, error } = await supabase
    .from('user_group_memberships')
    .select('security_groups!inner(display_name, description)')
    .eq('tenant_id', tenantId)
    .eq('user_id', userId);

  if (error) {
    throw new Error(`Failed to fetch user groups: ${error.message}`);
  }
  return data.map((item: any) => item.security_groups);
}

export async function getRolesForTenant(tenantId: string): Promise<any[]> {
  const { data, error } = await supabase
    .from('roles')
    .select('id, name, description')
    .eq('tenant_id', tenantId);

  if (error) {
    throw new Error(`Failed to fetch roles: ${error.message}`);
  }
  return data;
}

export async function createRole(tenantId: string, name: string, description: string): Promise<string> {
  const { data, error } = await supabase
    .from('roles')
    .insert({ name, tenant_id: tenantId, description })
    .select('id')
    .single();

  if (error) {
    throw new Error(`Failed to create role: ${error.message}`);
  }
  return data.id;
}

export async function assignRoleToUser(tenantId: string, userId: string, roleId: string): Promise<void> {
  const { error } = await supabase
    .from('user_roles')
    .upsert({
      tenant_id: tenantId,
      user_id: userId,
      role_id: roleId,
    }, { onConflict: 'tenant_id,user_id' });

  if (error) {
    throw new Error(`Failed to assign role to user: ${error.message}`);
  }

  await supabase
    .from('profiles')
    .update({ role_id: roleId })
    .eq('id', userId);
}

/**
 * Get application mappings for a specific role
 */
export async function getRoleApplicationMappings(tenantId: string, roleId: string): Promise<SupabaseRoleApplicationMapping[]> {
  const { data, error } = await supabase
    .from('role_application_mappings')
    .select('*, applications(name, description)')
    .eq('tenant_id', tenantId)
    .eq('role_id', roleId);

  if (error) {
    throw new Error(`Failed to fetch role application mappings: ${error.message}`);
  }
  return data;
}

/**
 * Get all role-application mappings for a tenant
 */
export async function getAllRoleApplicationMappings(tenantId: string): Promise<SupabaseRoleApplicationMapping[]> {
  const { data, error } = await supabase
    .from('role_application_mappings')
    .select('*, applications(name, description), roles(name, description)')
    .eq('tenant_id', tenantId);

  if (error) {
    throw new Error(`Failed to fetch role application mappings: ${error.message}`);
  }
  return data;
}

/**
 * Create a new role-application mapping
 */
export async function createRoleApplicationMapping(
  tenantId: string,
  roleId: string,
  applicationId: string,
  confidenceScore?: number
): Promise<string> {
  const now = new Date().toISOString();
  const { data, error } = await supabase
    .from('role_application_mappings')
    .upsert({
      tenant_id: tenantId,
      role_id: roleId,
      application_id: applicationId,
      confidence_score: confidenceScore || null,
      mapping_method: 'manual',
      mapping_version: '1.0',
      created_at: now,
      updated_at: now
    }, { onConflict: 'tenant_id,role_id,application_id' })
    .select('id')
    .single();

  if (error) {
    throw new Error(`Failed to create role-application mapping: ${error.message}`);
  }
  return data.id;
}

/**
 * Delete a role-application mapping
 */
export async function deleteRoleApplicationMapping(id: string): Promise<void> {
  const { error } = await supabase
    .from('role_application_mappings')
    .delete()
    .eq('id', id);

  if (error) {
    throw new Error(`Failed to delete role-application mapping: ${error.message}`);
  }
}