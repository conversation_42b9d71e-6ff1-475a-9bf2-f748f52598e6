import { PublicClientApplication, Configuration, InteractionRequiredAuthError, AccountInfo } from '@azure/msal-browser';

// Singleton MSAL instance
let msalInstance: PublicClientApplication | null = null;

// Authentication queue
let authPromise: Promise<string> | null = null;

/**
 * Gets or creates the MSAL instance
 * @param tenantId The Entra ID tenant ID
 * @param clientId The Entra ID client ID
 * @returns The MSAL instance
 */
export async function getMsalInstance(tenantId: string, clientId: string): Promise<PublicClientApplication> {
  if (msalInstance) {
    // Check if the instance matches the requested tenant and client
    const config = msalInstance.getConfiguration();
    if (config.auth.clientId === clientId && config.auth.authority?.includes(tenantId)) {
      return msalInstance;
    }
    // If not, clear the existing instance
    msalInstance = null;
  }

  const msalConfig: Configuration = {
    auth: {
      clientId: clientId,
      authority: `https://login.microsoftonline.com/${tenantId}`,
      redirectUri: window.location.origin,
    },
    cache: {
      cacheLocation: 'sessionStorage',
      storeAuthStateInCookie: false,
    },
  };
  
  msalInstance = new PublicClientApplication(msalConfig);
  await msalInstance.initialize();
  return msalInstance;
}

/**
 * Internal function to perform the actual token acquisition
 */
async function acquireTokenInternal(tenantId: string, clientId: string): Promise<string> {
  const instance = await getMsalInstance(tenantId, clientId);
  
  try {
    // Check if there's an account already signed in
    const accounts = instance.getAllAccounts();
    let account: AccountInfo | null = null;
    
    if (accounts.length === 0) {
      // No account, initiate login
      const loginResponse = await instance.loginPopup({
        scopes: ['https://graph.microsoft.com/.default'],
        prompt: 'select_account',
      });
      account = loginResponse.account;
      instance.setActiveAccount(account);
    } else {
      account = accounts[0];
      instance.setActiveAccount(account);
    }

    // Acquire token silently
    const tokenResponse = await instance.acquireTokenSilent({
      scopes: ['https://graph.microsoft.com/.default'],
      account,
    });

    return tokenResponse.accessToken;
  } catch (error) {
    if (error instanceof InteractionRequiredAuthError) {
      // Fallback to popup if silent token acquisition fails
      const tokenResponse = await instance.acquireTokenPopup({
        scopes: ['https://graph.microsoft.com/.default'],
        prompt: 'select_account',
      });
      return tokenResponse.accessToken;
    }
    throw error;
  }
}

/**
 * Gets an access token for Microsoft Graph API
 * @param tenantId The Entra ID tenant ID
 * @param clientId The Entra ID client ID
 * @returns The access token
 */
export async function getAccessToken(tenantId: string, clientId: string): Promise<string> {
  // If there's already an authentication in progress, wait for it
  if (authPromise) {
    try {
      return await authPromise;
    } catch (error) {
      // If the previous attempt failed, clear it and try again
      authPromise = null;
    }
  }

  // Start a new authentication attempt
  authPromise = acquireTokenInternal(tenantId, clientId);
  
  try {
    const token = await authPromise;
    return token;
  } finally {
    // Clear the promise when done (success or failure)
    authPromise = null;
  }
}

/**
 * Tests the connection to Entra ID
 * @param tenantId The Entra ID tenant ID
 * @param clientId The Entra ID client ID
 * @param clientSecret The Entra ID client secret (optional)
 * @returns True if the connection is successful
 */
export async function testEntraIdConnection(tenantId: string, clientId: string, clientSecret?: string): Promise<boolean> {
  try {
    if (!clientSecret) {
      // If no client secret provided, try MSAL flow for backward compatibility
      await getAccessToken(tenantId, clientId);
      return true;
    }

    // Test connection using Supabase edge function
    const response = await fetch(`${import.meta.env.VITE_SUPABASE_URL}/functions/v1/get-entra-token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${import.meta.env.VITE_SUPABASE_ANON_KEY}`,
      },
      body: JSON.stringify({
        tenant_id: tenantId,
        client_id: clientId,
        client_secret: clientSecret,
        test_only: true
      })
    });

    if (!response.ok) {
      return false;
    }

    const data = await response.json();
    return data.success === true;
  } catch (error) {
    console.error('Entra ID connection test failed:', error);
    return false;
  }
}

// Handle cleanup when the module is hot-reloaded
if (import.meta.hot) {
  import.meta.hot.dispose(() => {
    msalInstance = null;
    authPromise = null;
  });
} 