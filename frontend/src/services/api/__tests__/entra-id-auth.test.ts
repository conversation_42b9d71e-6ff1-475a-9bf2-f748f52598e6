import { describe, it, expect, vi, beforeEach } from 'vitest';
import { testEntraIdConnection } from '../entra-id-auth';

// Mock fetch
global.fetch = vi.fn();

// Mock environment variables
vi.stubEnv('VITE_SUPABASE_URL', 'https://mock-supabase-url.com');
vi.stubEnv('VITE_SUPABASE_ANON_KEY', 'mock-anon-key');

// Mock the MSAL library
vi.mock('@azure/msal-browser', () => {
  return {
    PublicClientApplication: vi.fn().mockImplementation(() => ({
      initialize: vi.fn().mockResolvedValue(undefined),
      getAllAccounts: vi.fn().mockReturnValue([]),
      setActiveAccount: vi.fn(),
      loginPopup: vi.fn().mockResolvedValue({
        account: { username: '<EMAIL>' },
      }),
      acquireTokenSilent: vi.fn().mockResolvedValue({
        accessToken: 'mock-access-token',
      }),
      acquireTokenPopup: vi.fn().mockResolvedValue({
        accessToken: 'mock-access-token-popup',
      }),
      getConfiguration: vi.fn().mockReturnValue({
        auth: {
          clientId: 'mock-client-id',
          authority: 'https://login.microsoftonline.com/mock-tenant-id',
        },
      }),
    })),
    InteractionRequiredAuthError: class InteractionRequiredAuthError extends Error {
      constructor() {
        super('Interaction required');
      }
    },
  };
});

describe('Entra ID Auth Service', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('testEntraIdConnection', () => {
    it('should test connection with client secret', async () => {
      // Mock fetch response
      vi.mocked(fetch).mockResolvedValueOnce({
        ok: true,
        json: vi.fn().mockResolvedValue({ success: true }),
      } as any);

      // Call the function
      const result = await testEntraIdConnection(
        'test-tenant-id',
        'test-client-id',
        'test-client-secret'
      );

      // Verify the result
      expect(result).toBe(true);

      // Verify that fetch was called with the correct arguments
      expect(fetch).toHaveBeenCalledWith(
        'https://mock-supabase-url.com/functions/v1/get-entra-token',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer mock-anon-key',
          },
          body: JSON.stringify({
            tenant_id: 'test-tenant-id',
            client_id: 'test-client-id',
            client_secret: 'test-client-secret',
            test_only: true,
          }),
        }
      );
    });

    it('should handle failed connection test', async () => {
      // Mock fetch response with an error
      vi.mocked(fetch).mockResolvedValueOnce({
        ok: false,
        json: vi.fn().mockResolvedValue({ success: false }),
      } as any);

      // Call the function
      const result = await testEntraIdConnection(
        'test-tenant-id',
        'test-client-id',
        'test-client-secret'
      );

      // Verify the result
      expect(result).toBe(false);
    });

    it('should handle exceptions during connection test', async () => {
      // Mock fetch to throw an error
      vi.mocked(fetch).mockRejectedValueOnce(new Error('Network error'));

      // Mock console.error to capture the error message
      const consoleErrorSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Call the function
      const result = await testEntraIdConnection(
        'test-tenant-id',
        'test-client-id',
        'test-client-secret'
      );

      // Verify the result
      expect(result).toBe(false);

      // Verify that the error was logged
      expect(consoleErrorSpy).toHaveBeenCalledWith(
        'Entra ID connection test failed:',
        expect.any(Error)
      );

      // Restore console.error
      consoleErrorSpy.mockRestore();
    });
  });
});
