// Explicitly import React to ensure it's available
import React, { useEffect, useState } from 'react';
import ReactDOM from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import App from './App';
import './styles/globalStyles.css.ts';
import { initializeAuth } from './stores/authStore';

// Configure future flags for React Router v7
const router = {
  future: {
    v7_startTransition: true,
    v7_relativeSplatPath: true
  }
};

// Create a wrapper component that initializes auth before rendering the app
const AppWithAuth: React.FC = () => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [initTimeout, setInitTimeout] = useState(false);

  useEffect(() => {
    // Set a timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      setInitTimeout(true);
    }, 3000); // 3 seconds timeout

    const init = async () => {
      try {
        await initializeAuth();
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        setIsInitialized(true);
        clearTimeout(timeoutId);
      }
    };

    init();

    return () => clearTimeout(timeoutId);
  }, []);

  // Render the app if initialization is complete or if timeout is reached
  if (!isInitialized && !initTimeout) {
    return <div style={{ padding: '20px', margin: '20px', backgroundColor: 'white', border: '1px solid #ccc', borderRadius: '5px' }}>Loading application...</div>;
  }

  return (
    <BrowserRouter {...router}>
      <App />
    </BrowserRouter>
  );
};

// Get the root element and create the root
const rootElement = document.getElementById('root');

if (!rootElement) {
  console.error('Root element not found!');
} else {
  const root = ReactDOM.createRoot(rootElement);

  // Wrap rendering in try/catch to catch any errors
  try {
    root.render(
      <React.StrictMode>
        <AppWithAuth />
      </React.StrictMode>
    );
  } catch (error) {
    console.error('Error rendering React application:', error);

    // Show a fallback UI if rendering fails
    rootElement.innerHTML = `
      <div style="padding: 20px; margin: 20px; background-color: #ffebee; border: 1px solid #ffcdd2; border-radius: 5px;">
        <h2>Error Rendering Application</h2>
        <p>An error occurred while rendering the application:</p>
        <pre style="background-color: #f5f5f5; padding: 10px; border-radius: 4px; overflow: auto;">${error}</pre>
      </div>
    `;
  }
}