import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import { User } from '@supabase/supabase-js';
import { supabase } from '../services/supabaseClient';
import { UserProfile } from '../types/supabase';
import { AuthError, AuthResult, AuthStatus, AuthCredentials, RegisterCredentials } from '../types/auth';

// Define the state type
interface AuthState {
  user: User | null;
  status: AuthStatus;
  userDetails: UserProfile | null;
  tenantId: string | null;
  permissions: string[];
  signIn: (credentials: AuthCredentials) => Promise<AuthResult<User>>;
  signUp: (credentials: RegisterCredentials) => Promise<AuthResult<User>>;
  signOut: () => Promise<AuthResult>;
  resetPassword: (email: string) => Promise<AuthResult>;
}

type AuthStore = AuthState;

const fetchUserProfile = async (userId: string): Promise<UserProfile | null> => {
  try {
    // First check if the profiles table exists
    const { error: tableCheckError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);

    if (tableCheckError) {
      if (tableCheckError.code === '42P01') {
        // Create a default profile in memory
        const userData = await supabase.auth.getUser();
        return {
          id: userId,
          name: 'Default User',
          email: userData.data.user?.email || '',
          tenant_id: 'default',
          role_id: 'default',
          created_at: new Date().toISOString()
        };
      }
      throw tableCheckError;
    }

    const { data, error } = await supabase
      .from('profiles')
      .select('id, name, email, tenant_id, role_id, created_at')
      .eq('id', userId)
      .maybeSingle();

    if (error) {
      throw error;
    }

    if (!data) {
      // Get user email from auth
      const { data: userData } = await supabase.auth.getUser();
      const userEmail = userData.user?.email || '';

      // Create a default profile if it doesn't exist
      const { error: createError } = await supabase
        .from('profiles')
        .insert([
          {
            id: userId,
            name: 'Default User',
            email: userEmail,
            tenant_id: 'default',
            role_id: 'default',
            created_at: new Date().toISOString()
          }
        ]);

      if (createError) {
        throw new Error('Failed to create user profile. Please contact support.');
      }

      // Fetch the newly created profile
      const { data: newProfile, error: fetchError } = await supabase
        .from('profiles')
        .select('id, name, email, tenant_id, role_id, created_at')
        .eq('id', userId)
        .single();

      if (fetchError || !newProfile) {
        throw new Error('Failed to retrieve user profile. Please try again.');
      }

      return newProfile as UserProfile;
    }

    return data as UserProfile;
  } catch (error) {
    console.error('Failed to fetch user profile:', error);
    // Return a default profile on error
    const userData = await supabase.auth.getUser();
    return {
      id: userId,
      name: 'Default User',
      email: userData.data.user?.email || '',
      tenant_id: 'default',
      role_id: 'default',
      created_at: new Date().toISOString()
    };
  }
};

const fetchUserPermissions = async (userId: string, tenantId: string): Promise<string[]> => {
  try {
    const { data, error } = await supabase
      .from('user_roles')
      .select('role_id')
      .eq('user_id', userId)
      .eq('tenant_id', tenantId);

    if (error) {
      // If the table doesn't exist, return default permissions
      if (error.code === '42P01') {
        return ['user']; // Default permission for new users
      }
      throw error;
    }
    return data.map(item => item.role_id);
  } catch (error) {
    console.error('Failed to fetch user permissions:', error);
    return ['user']; // Default permission on error
  }
};

// Create the store with improved type safety
export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      status: AuthStatus.LOADING,
      userDetails: null,
      tenantId: null,
      permissions: [],

      signIn: async ({ email, password }: AuthCredentials): Promise<AuthResult<User>> => {
        try {
          const { data, error: supabaseError } = await supabase.auth.signInWithPassword({
            email,
            password,
          });

          if (supabaseError) {
            return {
              data: null,
              error: {
                message: supabaseError.message,
                code: supabaseError.name,
                status: 401,
                originalError: supabaseError,
              },
            };
          }

          set({ user: data.user, status: AuthStatus.AUTHENTICATED });
          return { data: data.user, error: null };
        } catch (error) {
          return {
            data: null,
            error: {
              message: 'An unexpected error occurred during sign in',
              originalError: error,
            },
          };
        }
      },

      signUp: async ({ email, password, name }: RegisterCredentials): Promise<AuthResult<User>> => {
        try {
          const { data, error: supabaseError } = await supabase.auth.signUp({
            email,
            password,
            options: {
              data: { name },
            },
          });

          if (supabaseError) {
            return {
              data: null,
              error: {
                message: supabaseError.message,
                code: supabaseError.name,
                originalError: supabaseError,
              },
            };
          }

          return { data: data.user, error: null };
        } catch (error) {
          return {
            data: null,
            error: {
              message: 'An unexpected error occurred during sign up',
              originalError: error,
            },
          };
        }
      },

      signOut: async (): Promise<AuthResult> => {
        try {
          const { error: supabaseError } = await supabase.auth.signOut();

          if (supabaseError) {
            return {
              data: null,
              error: {
                message: supabaseError.message,
                code: supabaseError.name,
                originalError: supabaseError,
              },
            };
          }

          set({ 
            user: null, 
            status: AuthStatus.UNAUTHENTICATED,
            userDetails: null,
            tenantId: null,
            permissions: [],
          });
          return { data: null, error: null };
        } catch (error) {
          return {
            data: null,
            error: {
              message: 'An unexpected error occurred during sign out',
              originalError: error,
            },
          };
        }
      },

      resetPassword: async (email: string): Promise<AuthResult> => {
        try {
          const { error: supabaseError } = await supabase.auth.resetPasswordForEmail(email);

          if (supabaseError) {
            return {
              data: null,
              error: {
                message: supabaseError.message,
                code: supabaseError.name,
                originalError: supabaseError,
              },
            };
          }

          return { data: null, error: null };
        } catch (error) {
          return {
            data: null,
            error: {
              message: 'An unexpected error occurred during password reset',
              originalError: error,
            },
          };
        }
      },
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);

// Remove debug function since it's no longer needed
export const debugAuthStorage = () => {};

// Initialize auth state
export const initializeAuth = async () => {
  // Set a timeout to ensure loading state is reset
  const timeoutId = setTimeout(() => {
    useAuthStore.setState({ status: AuthStatus.LOADING });
  }, 5000);

  try {
    useAuthStore.setState({ status: AuthStatus.LOADING });

    // Check if we already have user data in localStorage
    const currentState = useAuthStore.getState();
    if (currentState.user && currentState.userDetails) {
      useAuthStore.setState({ status: AuthStatus.AUTHENTICATED });
      clearTimeout(timeoutId);
      return;
    }

    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    if (sessionError) {
      console.error('Error getting session:', sessionError);
      useAuthStore.setState({ status: AuthStatus.UNAUTHENTICATED });
      clearTimeout(timeoutId);
      return;
    }

    if (!session?.user) {
      useAuthStore.setState({ status: AuthStatus.UNAUTHENTICATED });
      clearTimeout(timeoutId);
      return;
    }

    // Only proceed with profile fetch if we don't already have user details or if the user ID has changed
    if (!currentState.userDetails || currentState.user?.id !== session.user.id) {
      // Set user immediately
      useAuthStore.setState({ user: session.user, status: AuthStatus.AUTHENTICATED });

      try {
        const profile = await fetchUserProfile(session.user.id);
        if (!profile) {
          console.error('Failed to fetch or create profile');
          useAuthStore.setState({ status: AuthStatus.UNAUTHENTICATED });
          clearTimeout(timeoutId);
          return;
        }

        const permissions = await fetchUserPermissions(session.user.id, profile.tenant_id);

        // Update all state at once to reduce re-renders
        useAuthStore.setState({
          userDetails: profile,
          tenantId: profile.tenant_id,
          permissions,
          status: AuthStatus.AUTHENTICATED
        });
      } catch (error) {
        console.error('Error during profile initialization:', error);
        useAuthStore.setState({ status: AuthStatus.UNAUTHENTICATED });
      }
    } else {
      useAuthStore.setState({ status: AuthStatus.AUTHENTICATED });
    }
  } catch (error) {
    console.error('Error during auth initialization:', error);
    useAuthStore.setState({ status: AuthStatus.UNAUTHENTICATED });
  } finally {
    clearTimeout(timeoutId);
  }
};

// Set up auth state change listener
export const setupAuthListener = () => {
  let timeoutId: NodeJS.Timeout | null = null;

  const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
    // Clear any existing timeout
    if (timeoutId) {
      clearTimeout(timeoutId);
      timeoutId = null;
    }

    // Set a new timeout to prevent infinite loading
    timeoutId = setTimeout(() => {
      const currentState = useAuthStore.getState();
      if (currentState.status === AuthStatus.LOADING) {
        useAuthStore.setState({ status: AuthStatus.UNAUTHENTICATED });
      }
    }, 5000);

    if (event === 'SIGNED_OUT') {
      // Clear all auth state at once
      useAuthStore.setState({
        user: null,
        status: AuthStatus.UNAUTHENTICATED,
        userDetails: null,
        tenantId: null,
        permissions: [],
      });

      // Clear localStorage to ensure clean state
      localStorage.removeItem('auth-storage');

      // Clear the timeout since we've reset loading state
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
    } else if (event === 'SIGNED_IN') {
      // Re-initialize auth to get the latest data
      try {
        await initializeAuth();
      } catch (error) {
        console.error('Error initializing auth after sign in:', error);
        useAuthStore.setState({ status: AuthStatus.UNAUTHENTICATED });
      } finally {
        // Clear the timeout since initializeAuth should have reset loading state
        if (timeoutId) {
          clearTimeout(timeoutId);
          timeoutId = null;
        }
      }
    } else if (event === 'TOKEN_REFRESHED') {
      if (session?.user) {
        // Just update the user object to keep the token fresh
        useAuthStore.setState({ user: session.user, status: AuthStatus.AUTHENTICATED });
      }

      // Clear the timeout since we've reset loading state
      if (timeoutId) {
        clearTimeout(timeoutId);
        timeoutId = null;
      }
    }
  });

  // Return the unsubscribe function
  return () => {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
    subscription.unsubscribe();
  };
};

// Initialize the auth listener
const unsubscribe = setupAuthListener();

// Ensure the listener is cleaned up when the module is hot-reloaded
if (import.meta.hot) {
  import.meta.hot.dispose(() => {
    unsubscribe();
  });
}