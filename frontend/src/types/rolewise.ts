export interface RolewiseRole {
    id: string;
    name: string;
    description: string;
    tenantId: string;
    source?: string;
    inferenceMethod?: string;
    inferenceVersion?: string;
    inferenceDate?: string;
    confidenceScore?: number;
    metadata?: Record<string, any>;
    createdAt?: string;
    updatedAt?: string;
  }

  export interface JobFunction {
    title: string;
    description: string;
  }

  export interface RoleJobFunctionMapping {
    id?: string;
    roleId: string;
    tenantId?: string;
    jobFunctionTitle: string;
    confidence: number;
    mappingMethod?: string;
    mappingVersion?: string;
    metadata?: Record<string, any>;
    createdAt?: string;
    updatedAt?: string;
  }