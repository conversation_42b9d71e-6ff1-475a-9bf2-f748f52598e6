export interface EntraIdApplication {
    appId: string;
    displayName: string;
    description?: string;
  }
  
  export interface EntraIdGroup {
    groupId: string;
    displayName: string;
    description?: string;
  }
  
  export interface EntraIdUser {
    userId: string;
    displayName: string;
    email: string;
    groupIds: string[];
  }
  
  export interface EntraIdData {
    applications: EntraIdApplication[];
    groups: EntraIdGroup[];
    users: EntraIdUser[];
  }