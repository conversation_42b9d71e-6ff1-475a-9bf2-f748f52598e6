export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      analyzed_subject_application_access: {
        Row: {
          access_granted_at: string | null
          analyzed_subject_id: string
          application_id: string
          tenant_id: string
        }
        Insert: {
          access_granted_at?: string | null
          analyzed_subject_id: string
          application_id: string
          tenant_id: string
        }
        Update: {
          access_granted_at?: string | null
          analyzed_subject_id?: string
          application_id?: string
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "analyzed_subject_application_access_analyzed_subject_id_fkey"
            columns: ["analyzed_subject_id"]
            isOneToOne: false
            referencedRelation: "analyzed_subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "analyzed_subject_application_access_application_id_fkey"
            columns: ["application_id"]
            isOneToOne: false
            referencedRelation: "applications"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "analyzed_subject_application_access_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      analyzed_subject_group_access: {
        Row: {
          access_granted_at: string | null
          analyzed_subject_id: string
          group_id: string
          tenant_id: string
        }
        Insert: {
          access_granted_at?: string | null
          analyzed_subject_id: string
          group_id: string
          tenant_id: string
        }
        Update: {
          access_granted_at?: string | null
          analyzed_subject_id?: string
          group_id?: string
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "analyzed_subject_group_access_analyzed_subject_id_fkey"
            columns: ["analyzed_subject_id"]
            isOneToOne: false
            referencedRelation: "analyzed_subjects"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "analyzed_subject_group_access_group_id_fkey"
            columns: ["group_id"]
            isOneToOne: false
            referencedRelation: "groups"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "analyzed_subject_group_access_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      analyzed_subjects: {
        Row: {
          compliance_level: Database["public"]["Enums"]["compliance_level"] | null
          created_at: string | null
          embedding: string | null
          external_identifier: string
          id: string
          name: string
          tenant_id: string | null
        }
        Insert: {
          compliance_level?: Database["public"]["Enums"]["compliance_level"] | null
          created_at?: string | null
          embedding?: string | null
          external_identifier: string
          id?: string
          name: string
          tenant_id?: string | null
        }
        Update: {
          compliance_level?: Database["public"]["Enums"]["compliance_level"] | null
          created_at?: string | null
          embedding?: string | null
          external_identifier?: string
          id?: string
          name?: string
          tenant_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "analyzed_subjects_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      applications: {
        Row: {
          description: string | null
          embedding: string | null
          id: string
          name: string
          tenant_id: string | null
        }
        Insert: {
          description?: string | null
          embedding?: string | null
          id?: string
          name: string
          tenant_id?: string | null
        }
        Update: {
          description?: string | null
          embedding?: string | null
          id?: string
          name?: string
          tenant_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "applications_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      groups: {
        Row: {
          created_at: string | null
          description: string | null
          display_name: string
          embedding: string | null
          group_id: string
          id: string
          tenant_id: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          description?: string | null
          display_name: string
          embedding?: string | null
          group_id: string
          id?: string
          tenant_id?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          description?: string | null
          display_name?: string
          embedding?: string | null
          group_id?: string
          id?: string
          tenant_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "groups_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      integrations: {
        Row: {
          app_tenant_id: string | null
          client_id: string
          client_secret: string
          created_at: string | null
          id: string
          tenant_id: string
          updated_at: string | null
        }
        Insert: {
          app_tenant_id?: string | null
          client_id: string
          client_secret: string
          created_at?: string | null
          id?: string
          tenant_id: string
          updated_at?: string | null
        }
        Update: {
          app_tenant_id?: string | null
          client_id?: string
          client_secret?: string
          created_at?: string | null
          id?: string
          tenant_id?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "integrations_app_tenant_id_fkey"
            columns: ["app_tenant_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["tenant_id"]
          }
        ]
      }
      permissions: {
        Row: {
          description: string | null
          id: string
          name: string
          tenant_id: string | null
        }
        Insert: {
          description?: string | null
          id?: string
          name: string
          tenant_id?: string | null
        }
        Update: {
          description?: string | null
          id?: string
          name?: string
          tenant_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "permissions_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      profiles: {
        Row: {
          created_at: string | null
          email: string
          id: string
          name: string
          role_id: string
          tenant_id: string
        }
        Insert: {
          created_at?: string | null
          email: string
          id: string
          name: string
          role_id: string
          tenant_id: string
        }
        Update: {
          created_at?: string | null
          email?: string
          id?: string
          name?: string
          role_id?: string
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "profiles_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profiles_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: true
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      role_embeddings: {
        Row: {
          embedding: string | null
          role_id: string | null
          tenant_id: string | null
        }
        Insert: {
          embedding?: string | null
          role_id?: string | null
          tenant_id?: string | null
        }
        Update: {
          embedding?: string | null
          role_id?: string | null
          tenant_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "role_embeddings_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "role_embeddings_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      role_permissions: {
        Row: {
          permission_id: string
          role_id: string
          tenant_id: string
        }
        Insert: {
          permission_id: string
          role_id: string
          tenant_id: string
        }
        Update: {
          permission_id?: string
          role_id?: string
          tenant_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_role_permissions_permission_id"
            columns: ["permission_id"]
            isOneToOne: false
            referencedRelation: "permissions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_role_permissions_role_id"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "role_permissions_permission_id_fkey"
            columns: ["permission_id"]
            isOneToOne: false
            referencedRelation: "permissions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "role_permissions_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "role_permissions_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      roles: {
        Row: {
          description: string | null
          id: string
          name: string
          tenant_id: string | null
          source: string | null
          inference_method: string | null
          inference_version: string | null
          inference_date: string | null
          confidence_score: number | null
          metadata: Json | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          description?: string | null
          id?: string
          name: string
          tenant_id?: string | null
          source?: string | null
          inference_method?: string | null
          inference_version?: string | null
          inference_date?: string | null
          confidence_score?: number | null
          metadata?: Json | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          description?: string | null
          id?: string
          name?: string
          tenant_id?: string | null
          source?: string | null
          inference_method?: string | null
          inference_version?: string | null
          inference_date?: string | null
          confidence_score?: number | null
          metadata?: Json | null
          created_at?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "roles_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      tenants: {
        Row: {
          created_at: string | null
          created_by: string | null
          domain: string | null
          id: string
          name: string
        }
        Insert: {
          created_at?: string | null
          created_by?: string | null
          domain?: string | null
          id?: string
          name: string
        }
        Update: {
          created_at?: string | null
          created_by?: string | null
          domain?: string | null
          id?: string
          name?: string
        }
        Relationships: []
      }
      role_application_mappings: {
        Row: {
          id: string
          role_id: string
          tenant_id: string
          application_id: string
          confidence_score: number | null
          mapping_method: string | null
          mapping_version: string | null
          metadata: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          role_id: string
          tenant_id: string
          application_id: string
          confidence_score?: number | null
          mapping_method?: string | null
          mapping_version?: string | null
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          role_id?: string
          tenant_id?: string
          application_id?: string
          confidence_score?: number | null
          mapping_method?: string | null
          mapping_version?: string | null
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "role_application_mappings_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "role_application_mappings_application_id_fkey"
            columns: ["application_id"]
            isOneToOne: false
            referencedRelation: "applications"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "role_application_mappings_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      role_job_function_mappings: {
        Row: {
          id: string
          role_id: string
          tenant_id: string
          job_function_title: string
          confidence_score: number
          mapping_method: string | null
          mapping_version: string | null
          metadata: Json | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          role_id: string
          tenant_id: string
          job_function_title: string
          confidence_score: number
          mapping_method?: string | null
          mapping_version?: string | null
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          role_id?: string
          tenant_id?: string
          job_function_title?: string
          confidence_score?: number
          mapping_method?: string | null
          mapping_version?: string | null
          metadata?: Json | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "role_job_function_mappings_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "role_job_function_mappings_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      user_group_memberships: {
        Row: {
          created_at: string | null
          group_id: string
          id: string
          tenant_id: string | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          group_id: string
          id?: string
          tenant_id?: string | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          group_id?: string
          id?: string
          tenant_id?: string | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_group_memberships_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
      user_roles: {
        Row: {
          role_id: string
          tenant_id: string
          user_id: string
        }
        Insert: {
          role_id: string
          tenant_id: string
          user_id: string
        }
        Update: {
          role_id?: string
          tenant_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_roles_role_id_fkey"
            columns: ["role_id"]
            isOneToOne: false
            referencedRelation: "roles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_roles_tenant_id_fkey"
            columns: ["tenant_id"]
            isOneToOne: false
            referencedRelation: "tenants"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      create_tenant: {
        Args: { p_domain: string; p_name: string }
        Returns: string
      }
      create_user_profile: {
        Args: { p_user_id: string; p_email: string; p_name?: string }
        Returns: {
          profile_id: string
          profile_name: string
          profile_email: string
          profile_tenant_id: string
          profile_created_at: string
        }[]
      }
      get_current_user: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_page_parents: {
        Args: { page_id: number } | Record<PropertyKey, never>
        Returns: {
          id: number
          parent_page_id: number
          path: string
          meta: Json
        }[]
      }
      get_user_permissions: {
        Args: { p_user_id: string; p_tenant_id: string }
        Returns: string[]
      }
      get_user_tenant_id: {
        Args: { p_user_id: string }
        Returns: string
      }
      has_permission: {
        Args: {
          p_user_id: string
          p_permission_name: string
          p_tenant_id: string
        }
        Returns: boolean
      }
      match_page_sections: {
        Args:
          | Record<PropertyKey, never>
          | {
              embedding: string
              match_threshold: number
              match_count: number
              min_content_length: number
            }
        Returns: undefined
      }
    }
    Enums: {
      compliance_level: "Low" | "Medium" | "High"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (Database["public"]["Tables"] & Database["public"]["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (Database["public"]["Tables"] &
      Database["public"]["Views"])
  ? (Database["public"]["Tables"] &
      Database["public"]["Views"])[PublicTableNameOrOptions] extends {
      Row: infer R
    }
    ? R
    : never
  : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof Database["public"]["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof Database["public"]["Tables"]
  ? Database["public"]["Tables"][PublicTableNameOrOptions] extends {
      Insert: infer I
    }
    ? I
    : never
  : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof Database["public"]["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof Database["public"]["Tables"]
  ? Database["public"]["Tables"][PublicTableNameOrOptions] extends {
      Update: infer U
    }
    ? U
    : never
  : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof Database["public"]["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof Database["public"]["Enums"]
  ? Database["public"]["Enums"][PublicEnumNameOrOptions]
  : never
```