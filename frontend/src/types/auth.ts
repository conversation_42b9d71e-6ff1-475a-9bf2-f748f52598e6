export interface AuthError {
  message: string;
  code?: string;
  status?: number;
  originalError?: unknown;
}

export type AuthResult<T = void> = {
  data: T | null;
  error: AuthError | null;
};

export enum AuthStatus {
  AUTHENTICATED = 'authenticated',
  UNAUTHENTICATED = 'unauthenticated',
  LOADING = 'loading'
}

export interface AuthCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials extends AuthCredentials {
  name: string;
} 