import { useState, useEffect } from 'react';
import { useRolewiseStore } from '../stores/rolewiseStore';
import * as styles from './permissionsStyles.css.ts';

const Permissions = () => {
  const { roles, loading, error } = useRolewiseStore();
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedRole, setSelectedRole] = useState<string | null>(null);

  const filteredRoles = roles.filter(
    (role) =>
      role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      role.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className={styles.container}>
      <h1 className={styles.title}>Permissions</h1>
      {error && <div className={styles.error}>{error}</div>}
      {loading ? (
        <div>Loading...</div>
      ) : (
        <div className={styles.content}>
          <div className={styles.filters}>
            <input
              type="text"
              placeholder="Search roles..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
          </div>
          <div className={styles.roleList}>
            {filteredRoles.map((role) => (
              <div
                key={role.id}
                className={`${styles.roleCard} ${selectedRole === role.id ? styles.selected : ''}`}
                onClick={() => setSelectedRole(role.id)}
              >
                <div className={styles.roleInfo}>
                  <h3 className={styles.roleName}>{role.name}</h3>
                  <p className={styles.roleDescription}>{role.description}</p>
                </div>
                <div className={styles.roleActions}>
                  <button className={styles.editButton}>Edit Permissions</button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Permissions;