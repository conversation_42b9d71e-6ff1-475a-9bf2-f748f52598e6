import { style } from '@vanilla-extract/css';

// Existing styles
export const permissionsContainer = style({
  padding: '1.5rem',
  backgroundColor: 'white',
});

export const titleStyle = style({
  fontSize: '1.5rem',
  fontWeight: 'bold',
  color: '#1f2937', // Equivalent to text-gray-900
});

export const buttonStyle = style({
  padding: '0.5rem 1rem',
  backgroundColor: '#3b82f6', // Equivalent to bg-primary-600
  color: 'white',
  borderRadius: '0.375rem',
  cursor: 'pointer',
});

export const inputStyle = style({
  padding: '0.5rem',
  borderRadius: '0.375rem',
  border: '1px solid #d1d5db', // Equivalent to border-gray-300
  width: '100%',
});

export const loadingStyle = style({
  display: 'flex',
  justifyContent: 'center',
  padding: '3rem',
});

export const errorStyle = style({
  backgroundColor: '#fee2e2', // Equivalent to bg-red-50
  borderLeft: '4px solid #f87171', // Equivalent to border-red-500
  padding: '1rem',
});

// New styles to replace Tailwind classes
// Header (replaces flex justify-between items-center)
export const headerStyle = style({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
});

// Filter section (replaces flex flex-col md:flex-row md:justify-between md:items-center space-y-4 md:space-y-0)
export const filterSectionStyle = style({
  display: 'flex',
  flexDirection: 'column',
  gap: '1rem',
  '@media': {
    'screen and (min-width: 768px)': {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      gap: 0,
    },
  },
});

// Search wrapper (replaces relative w-full md:w-64)
export const searchWrapperStyle = style({
  position: 'relative',
  width: '100%',
  '@media': {
    'screen and (min-width: 768px)': {
      width: '16rem',
    },
  },
});

// Search icon wrapper (replaces absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none)
export const searchIconWrapperStyle = style({
  position: 'absolute',
  top: 0,
  bottom: 0,
  left: 0,
  paddingLeft: '0.75rem',
  display: 'flex',
  alignItems: 'center',
  pointerEvents: 'none',
});

// Search icon (replaces h-5 w-5 text-gray-400)
export const searchIconStyle = style({
  height: '1.25rem',
  width: '1.25rem',
  color: '#9ca3af',
});

// Filter wrapper (replaces flex items-center space-x-2)
export const filterWrapperStyle = style({
  display: 'flex',
  alignItems: 'center',
  gap: '0.5rem',
});

// Filter label (replaces text-sm font-medium text-gray-700)
export const filterLabelStyle = style({
  fontSize: '0.875rem',
  fontWeight: '500',
  color: '#374151',
});

// Table container (replaces overflow-x-auto)
export const tableContainerStyle = style({
  overflowX: 'auto',
});

// Table (replaces min-w-full divide-y divide-gray-200)
export const tableStyle = style({
  minWidth: '100%',
  borderCollapse: 'collapse',
});

// Table head (replaces bg-gray-50)
export const tableHeadStyle = style({
  backgroundColor: '#f9fafb',
  borderBottom: '1px solid #e5e7eb',
});

// Table header cell (replaces px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider)
export const tableHeaderCellStyle = style({
  paddingLeft: '1.5rem',
  paddingRight: '1.5rem',
  paddingTop: '0.75rem',
  paddingBottom: '0.75rem',
  textAlign: 'left',
  fontSize: '0.75rem',
  fontWeight: '500',
  color: '#6b7280',
  textTransform: 'uppercase',
  letterSpacing: '0.05em',
});

// Table header cell for actions (replaces relative px-6 py-3)
export const tableHeaderActionsCellStyle = style({
  position: 'relative',
  paddingLeft: '1.5rem',
  paddingRight: '1.5rem',
  paddingTop: '0.75rem',
  paddingBottom: '0.75rem',
});

// Screen-reader-only (replaces sr-only)
export const srOnlyStyle = style({
  position: 'absolute',
  width: '1px',
  height: '1px',
  padding: 0,
  margin: '-1px',
  overflow: 'hidden',
  clip: 'rect(0, 0, 0, 0)',
  whiteSpace: 'nowrap',
  borderWidth: 0,
});

// Table body (replaces bg-white divide-y divide-gray-200)
export const tableBodyStyle = style({
  backgroundColor: 'white',
  '& > tr': {
    borderBottom: '1px solid #e5e7eb',
  },
});

// No permissions cell (replaces px-6 py-4 text-center text-gray-500)
export const noPermissionsCellStyle = style({
  paddingLeft: '1.5rem',
  paddingRight: '1.5rem',
  paddingTop: '1rem',
  paddingBottom: '1rem',
  textAlign: 'center',
  color: '#6b7280',
});

// Table cell (replaces px-6 py-4 whitespace-nowrap)
export const tableCellStyle = style({
  paddingLeft: '1.5rem',
  paddingRight: '1.5rem',
  paddingTop: '1rem',
  paddingBottom: '1rem',
  whiteSpace: 'nowrap',
});

// Table cell for description (replaces px-6 py-4)
export const tableCellDescriptionStyle = style({
  paddingLeft: '1.5rem',
  paddingRight: '1.5rem',
  paddingTop: '1rem',
  paddingBottom: '1rem',
});

// Permission name (replaces text-sm font-medium text-gray-900)
export const permissionNameStyle = style({
  fontSize: '0.875rem',
  fontWeight: '500',
  color: '#111827',
});

// Resource badge (replaces px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800)
export const resourceBadgeStyle = style({
  paddingLeft: '0.5rem',
  paddingRight: '0.5rem',
  display: 'inline-flex',
  fontSize: '0.75rem',
  lineHeight: '1.25rem',
  fontWeight: '600',
  borderRadius: '9999px',
  backgroundColor: '#dbeafe',
  color: '#1e40af',
});

// Action badge (replaces px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800)
export const actionBadgeStyle = style({
  paddingLeft: '0.5rem',
  paddingRight: '0.5rem',
  display: 'inline-flex',
  fontSize: '0.75rem',
  lineHeight: '1.25rem',
  fontWeight: '600',
  borderRadius: '9999px',
  backgroundColor: '#dcfce7',
  color: '#166534',
});

// Description text (replaces text-sm text-gray-500)
export const descriptionTextStyle = style({
  fontSize: '0.875rem',
  color: '#6b7280',
});

// Actions cell (replaces px-6 py-4 whitespace-nowrap text-right text-sm font-medium)
export const actionsCellStyle = style({
  paddingLeft: '1.5rem',
  paddingRight: '1.5rem',
  paddingTop: '1rem',
  paddingBottom: '1rem',
  whiteSpace: 'nowrap',
  textAlign: 'right',
  fontSize: '0.875rem',
  fontWeight: '500',
});

// Edit button (replaces text-primary-600 hover:text-primary-900 mr-4)
export const editButtonStyle = style({
  color: '#2563eb',
  ':hover': {
    color: '#1e3a8a',
  },
  marginRight: '1rem',
  background: 'none',
  border: 'none',
  cursor: 'pointer',
});

// Delete button (replaces text-red-600 hover:text-red-900)
export const deleteButtonStyle = style({
  color: '#dc2626',
  ':hover': {
    color: '#991b1b',
  },
  background: 'none',
  border: 'none',
  cursor: 'pointer',
});

export const container = {
  padding: '2rem',
  maxWidth: '1200px',
  margin: '0 auto',
};

export const title = {
  fontSize: '2rem',
  fontWeight: 'bold',
  marginBottom: '1.5rem',
  color: '#1a1a1a',
};

export const error = {
  color: '#dc2626',
  backgroundColor: '#fee2e2',
  padding: '1rem',
  borderRadius: '0.5rem',
  marginBottom: '1rem',
};

export const content = {
  display: 'flex',
  flexDirection: 'column' as const,
  gap: '1.5rem',
};

export const filters = {
  display: 'flex',
  gap: '1rem',
  marginBottom: '1rem',
};

export const searchInput = {
  flex: 1,
  padding: '0.5rem 1rem',
  borderRadius: '0.375rem',
  border: '1px solid #d1d5db',
  fontSize: '1rem',
  '&:focus': {
    outline: 'none',
    borderColor: '#3b82f6',
    boxShadow: '0 0 0 2px rgba(59, 130, 246, 0.2)',
  },
};

export const roleList = {
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
  gap: '1rem',
};

export const roleCard = {
  backgroundColor: 'white',
  borderRadius: '0.5rem',
  padding: '1rem',
  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
  cursor: 'pointer',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    transform: 'translateY(-2px)',
  },
};

export const selected = {
  border: '2px solid #3b82f6',
  backgroundColor: '#f0f7ff',
};

export const roleInfo = {
  marginBottom: '1rem',
};

export const roleName = {
  fontSize: '1.25rem',
  fontWeight: 'bold',
  marginBottom: '0.5rem',
  color: '#1a1a1a',
};

export const roleDescription = {
  color: '#6b7280',
  marginBottom: '0.5rem',
};

export const roleActions = {
  display: 'flex',
  gap: '0.5rem',
};

export const editButton = {
  padding: '0.5rem 1rem',
  borderRadius: '0.375rem',
  backgroundColor: '#3b82f6',
  color: 'white',
  border: 'none',
  cursor: 'pointer',
  '&:hover': {
    backgroundColor: '#2563eb',
  },
};