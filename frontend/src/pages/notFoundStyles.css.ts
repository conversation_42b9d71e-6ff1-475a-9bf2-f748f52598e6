import { style } from '@vanilla-extract/css';

// Existing styles
export const notFoundContainer = style({
  minHeight: '100vh',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: '#f9fafb', // Equivalent to bg-gray-50
  padding: '3rem',
});

export const titleStyle = style({
  fontSize: '6rem',
  fontWeight: 'bold',
  color: '#3b82f6', // Equivalent to text-primary-600
});

export const messageStyle = style({
  marginTop: '1rem',
  color: '#4b5563', // Equivalent to text-gray-600
});

export const buttonStyle = style({
  padding: '0.5rem 1rem',
  backgroundColor: '#3b82f6', // Equivalent to bg-primary-600
  color: 'white',
  borderRadius: '0.375rem',
  cursor: 'pointer',
  textDecoration: 'none', // Ensure no underline for <Link>
  display: 'inline-block', // Ensure the Link behaves like a button
});

// New styles to replace Tailwind classes
// Content container (replaces max-w-md w-full text-center)
export const contentContainerStyle = style({
  maxWidth: '28rem',
  width: '100%',
  textAlign: 'center',
});

// Subtitle (replaces mt-4 text-3xl font-bold text-gray-900)
export const subtitleStyle = style({
  marginTop: '1rem',
  fontSize: '1.875rem',
  fontWeight: '700',
  color: '#111827',
});

// Button wrapper (replaces mt-6)
export const buttonWrapperStyle = style({
  marginTop: '1.5rem',
});

export const container = {
  display: 'flex',
  flexDirection: 'column' as const,
  alignItems: 'center',
  justifyContent: 'center',
  minHeight: '100vh',
  padding: '2rem',
  textAlign: 'center' as const,
};

export const title = {
  fontSize: '3rem',
  fontWeight: 'bold',
  marginBottom: '1rem',
  color: '#1a1a1a',
};

export const message = {
  fontSize: '1.25rem',
  color: '#6b7280',
  marginBottom: '2rem',
};

export const link = {
  padding: '0.75rem 1.5rem',
  borderRadius: '0.375rem',
  backgroundColor: '#3b82f6',
  color: 'white',
  textDecoration: 'none',
  fontWeight: 'bold',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    backgroundColor: '#2563eb',
  },
};