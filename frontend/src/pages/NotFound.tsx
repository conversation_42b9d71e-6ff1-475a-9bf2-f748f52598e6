import { Link } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import * as styles from './notFoundStyles.css.ts';

const NotFound = () => {
  const { user } = useAuthStore();

  return (
    <div className={styles.container}>
      <h1 className={styles.title}>404 - Page Not Found</h1>
      <p className={styles.message}>
        The page you are looking for does not exist.
      </p>
      <Link to={user ? '/' : '/login'} className={styles.link}>
        Go to {user ? 'Dashboard' : 'Login'}
      </Link>
    </div>
  );
};

export default NotFound;