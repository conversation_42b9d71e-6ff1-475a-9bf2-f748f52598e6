import { style } from '@vanilla-extract/css';

// Existing styles
export const rolesContainer = style({
  padding: '1.5rem',
  backgroundColor: 'white',
});

export const titleStyle = style({
  fontSize: '1.5rem',
  fontWeight: 'bold',
  color: '#1f2937', // Equivalent to text-gray-900
});

export const buttonStyle = style({
  padding: '0.5rem 1rem',
  backgroundColor: '#3b82f6', // Equivalent to bg-primary-600
  color: 'white',
  borderRadius: '0.375rem',
  cursor: 'pointer',
});

export const inputStyle = style({
  padding: '0.5rem',
  borderRadius: '0.375rem',
  border: '1px solid #d1d5db', // Equivalent to border-gray-300
  width: '100%',
});

export const loadingStyle = style({
  display: 'flex',
  justifyContent: 'center',
  padding: '3rem',
});

export const errorStyle = style({
  backgroundColor: '#fee2e2', // Equivalent to bg-red-50
  borderLeft: '4px solid #f87171', // Equivalent to border-red-500
  padding: '1rem',
});

// New styles to replace Tailwind classes
// Header (replaces flex justify-between items-center)
export const headerStyle = style({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
});

// Search section (replaces flex justify-between items-center)
export const searchSectionStyle = style({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
});

// Roles grid (replaces grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6)
export const rolesGridStyle = style({
  display: 'grid',
  gridTemplateColumns: '1fr',
  gap: '1.5rem',
  '@media': {
    'screen and (min-width: 768px)': {
      gridTemplateColumns: '1fr 1fr', // md:grid-cols-2
    },
    'screen and (min-width: 1024px)': {
      gridTemplateColumns: '1fr 1fr 1fr', // lg:grid-cols-3
    },
  },
});

// No roles message (replaces col-span-full text-center text-gray-500 py-8)
export const noRolesMessageStyle = style({
  gridColumn: '1 / -1',
  textAlign: 'center',
  color: '#6b7280',
  paddingTop: '2rem',
  paddingBottom: '2rem',
});

// Card (replaces card)
export const cardStyle = style({
  padding: '1rem',
  borderRadius: '0.375rem',
  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
  backgroundColor: 'white',
});

// Card header (replaces flex justify-between items-start)
export const cardHeaderStyle = style({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'flex-start',
});

// Card title (replaces text-lg font-semibold text-gray-900)
export const cardTitleStyle = style({
  fontSize: '1.125rem',
  fontWeight: '600',
  color: '#111827',
});

// Card description (replaces text-sm text-gray-500 mt-1)
export const cardDescriptionStyle = style({
  fontSize: '0.875rem',
  color: '#6b7280',
  marginTop: '0.25rem',
});

// Card actions (replaces flex space-x-2)
export const cardActionsStyle = style({
  display: 'flex',
  gap: '0.5rem',
});

// Edit icon (replaces text-primary-600 hover:text-primary-900)
export const editIconStyle = style({
  color: '#2563eb',
  ':hover': {
    color: '#1e3a8a',
  },
  background: 'none',
  border: 'none',
  cursor: 'pointer',
});

// Delete icon (replaces text-red-600 hover:text-red-900)
export const deleteIconStyle = style({
  color: '#dc2626',
  ':hover': {
    color: '#991b1b',
  },
  background: 'none',
  border: 'none',
  cursor: 'pointer',
});

// Icon (replaces h-5 w-5)
export const iconStyle = style({
  height: '1.25rem',
  width: '1.25rem',
});

// Card meta section (replaces mt-4 pt-4 border-t border-gray-100)
export const cardMetaSectionStyle = style({
  marginTop: '1rem',
  paddingTop: '1rem',
  borderTopWidth: '1px',
  borderColor: '#f3f4f6',
});

// Meta row (replaces flex justify-between text-sm)
export const metaRowStyle = style({
  display: 'flex',
  justifyContent: 'space-between',
  fontSize: '0.875rem',
});

// Meta row with margin (replaces flex justify-between text-sm mt-2)
export const metaRowWithMarginStyle = style({
  display: 'flex',
  justifyContent: 'space-between',
  fontSize: '0.875rem',
  marginTop: '0.5rem',
});

// Meta label (replaces text-gray-500)
export const metaLabelStyle = style({
  color: '#6b7280',
});

// Meta value (replaces font-medium text-gray-900)
export const metaValueStyle = style({
  fontWeight: '500',
  color: '#111827',
});

// Meta date (replaces text-gray-900)
export const metaDateStyle = style({
  color: '#111827',
});

// Button wrapper (replaces mt-4)
export const buttonWrapperStyle = style({
  marginTop: '1rem',
});

// Manage permissions button (replaces w-full btn btn-outline text-sm)
export const managePermissionsButtonStyle = style({
  width: '100%',
  padding: '0.5rem 1rem',
  border: '1px solid #d1d5db',
  backgroundColor: 'transparent',
  borderRadius: '0.375rem',
  fontSize: '0.875rem',
  color: '#374151',
  cursor: 'pointer',
  ':hover': {
    backgroundColor: '#f3f4f6',
  },
});

export const container = {
  padding: '2rem',
  maxWidth: '1200px',
  margin: '0 auto',
};

export const title = {
  fontSize: '2rem',
  fontWeight: 'bold',
  marginBottom: '1.5rem',
  color: '#1a1a1a',
};

export const error = {
  color: '#dc2626',
  backgroundColor: '#fee2e2',
  padding: '1rem',
  borderRadius: '0.5rem',
  marginBottom: '1rem',
};

export const content = {
  display: 'flex',
  flexDirection: 'column' as const,
  gap: '1.5rem',
};

export const filters = {
  display: 'flex',
  gap: '1rem',
  marginBottom: '1rem',
};

export const searchInput = {
  flex: 1,
  padding: '0.5rem 1rem',
  borderRadius: '0.375rem',
  border: '1px solid #d1d5db',
  fontSize: '1rem',
  '&:focus': {
    outline: 'none',
    borderColor: '#3b82f6',
    boxShadow: '0 0 0 2px rgba(59, 130, 246, 0.2)',
  },
};

export const roleList = {
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
  gap: '1rem',
};

export const roleCard = {
  backgroundColor: 'white',
  borderRadius: '0.5rem',
  padding: '1rem',
  boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
  cursor: 'pointer',
  transition: 'all 0.2s ease-in-out',
  '&:hover': {
    boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
    transform: 'translateY(-2px)',
  },
};

export const selected = {
  border: '2px solid #3b82f6',
  backgroundColor: '#f0f7ff',
};

export const roleInfo = {
  marginBottom: '1rem',
};

export const roleName = {
  fontSize: '1.25rem',
  fontWeight: 'bold',
  marginBottom: '0.5rem',
  color: '#1a1a1a',
};

export const roleDescription = {
  color: '#6b7280',
  marginBottom: '0.5rem',
};

export const roleSource = {
  fontSize: '0.875rem',
  color: '#6b7280',
  marginBottom: '0.5rem',
  display: 'flex',
  alignItems: 'center',
  gap: '0.25rem',
};

export const manualSource = {
  color: '#059669', // Green color for manual source
  fontWeight: 'bold',
};

export const inferredSource = {
  color: '#6366f1', // Purple color for AI inferred
  fontWeight: 'bold',
};

export const confidenceScore = {
  fontSize: '0.875rem',
  color: '#6b7280',
  marginBottom: '0.5rem',
};

export const roleActions = {
  display: 'flex',
  gap: '0.5rem',
};

export const editButton = {
  padding: '0.5rem 1rem',
  borderRadius: '0.375rem',
  backgroundColor: '#3b82f6',
  color: 'white',
  border: 'none',
  cursor: 'pointer',
  '&:hover': {
    backgroundColor: '#2563eb',
  },
};

export const deleteButton = {
  padding: '0.5rem 1rem',
  borderRadius: '0.375rem',
  backgroundColor: '#ef4444',
  color: 'white',
  border: 'none',
  cursor: 'pointer',
  '&:hover': {
    backgroundColor: '#dc2626',
  },
};