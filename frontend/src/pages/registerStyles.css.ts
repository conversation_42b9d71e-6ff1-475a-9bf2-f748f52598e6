import { style, keyframes } from '@vanilla-extract/css';

// Existing styles
export const registerContainer = style({
  minHeight: '100vh',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: '#f9fafb', // Equivalent to bg-gray-50
  padding: '3rem',
});

export const titleStyle = style({
  textAlign: 'center',
  fontSize: '2rem',
  fontWeight: 'bold',
  color: '#1f2937', // Equivalent to text-gray-900
});

export const inputStyle = style({
  padding: '0.5rem',
  borderRadius: '0.375rem',
  border: '1px solid #d1d5db', // Equivalent to border-gray-300
  width: '100%',
});

export const buttonStyle = style({
  padding: '0.5rem 1rem',
  backgroundColor: '#3b82f6', // Equivalent to bg-primary-600
  color: 'white',
  borderRadius: '0.375rem',
  cursor: 'pointer',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
});

export const errorStyle = style({
  backgroundColor: '#fee2e2', // Equivalent to bg-red-50
  borderLeft: '4px solid #f87171', // Equivalent to border-red-500
  padding: '1rem',
});

// New styles to replace Tailwind classes
// Form container (replaces max-w-md w-full space-y-8)
export const formContainerStyle = style({
  maxWidth: '28rem',
  width: '100%',
  display: 'flex',
  flexDirection: 'column',
  gap: '2rem',
});

// Subtitle (replaces mt-6 text-center text-2xl font-bold text-gray-900)
export const subtitleStyle = style({
  marginTop: '1.5rem',
  textAlign: 'center',
  fontSize: '1.5rem',
  fontWeight: '700',
  color: '#111827',
});

// Form (replaces mt-8 space-y-6)
export const formStyle = style({
  marginTop: '2rem',
  display: 'flex',
  flexDirection: 'column',
  gap: '1.5rem',
});

// Input wrapper (replaces rounded-md shadow-sm -space-y-px)
export const inputWrapperStyle = style({
  borderRadius: '0.375rem',
  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
  display: 'flex',
  flexDirection: 'column',
  gap: '0.25rem',
});

// Link wrapper (replaces flex items-center justify-between)
export const linkWrapperStyle = style({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
});

// Link text (replaces text-sm)
export const linkTextStyle = style({
  fontSize: '0.875rem',
});

// Sign-in link (replaces font-medium text-primary-600 hover:text-primary-500)
export const signInLinkStyle = style({
  fontWeight: '500',
  color: '#2563eb',
  ':hover': {
    color: '#3b82f6',
  },
  textDecoration: 'none',
});

// Spinning animation (replaces animate-spin)
const spin = keyframes({
  '0%': { transform: 'rotate(0deg)' },
  '100%': { transform: 'rotate(360deg)' },
});

// Spinner (replaces -ml-1 mr-3 h-5 w-5 text-white)
export const spinnerStyle = style({
  animation: `${spin} 1s linear infinite`,
  marginLeft: '-0.25rem',
  marginRight: '0.75rem',
  height: '1.25rem',
  width: '1.25rem',
  color: 'white',
});

// Spinner circle (replaces opacity-25)
export const spinnerCircleStyle = style({
  opacity: 0.25,
});

// Spinner path (replaces opacity-75)
export const spinnerPathStyle = style({
  opacity: 0.75,
});

// Confirmation container (replaces mt-8 space-y-6)
export const confirmationContainerStyle = style({
  marginTop: '2rem',
  display: 'flex',
  flexDirection: 'column',
  gap: '1.5rem',
});

// Success message (replaces bg-green-50 border-l-4 border-green-500 p-4)
export const successMessageStyle = style({
  backgroundColor: '#f0fdf4',
  borderLeftWidth: '4px',
  borderColor: '#22c55e',
  padding: '1rem',
});

// Success message wrapper (replaces flex)
export const successMessageWrapperStyle = style({
  display: 'flex',
});

// Success icon wrapper (replaces flex-shrink-0)
export const successIconWrapperStyle = style({
  flexShrink: 0,
});

// Success icon (replaces h-5 w-5 text-green-500)
export const successIconStyle = style({
  height: '1.25rem',
  width: '1.25rem',
  color: '#22c55e',
});

// Success text wrapper (replaces ml-3)
export const successTextWrapperStyle = style({
  marginLeft: '0.75rem',
});

// Success text (replaces text-sm text-green-700)
export const successTextStyle = style({
  fontSize: '0.875rem',
  color: '#15803d',
});

// Confirmation message (replaces text-center)
export const confirmationMessageStyle = style({
  textAlign: 'center',
});

// Confirmation title (replaces text-lg font-medium text-gray-900)
export const confirmationTitleStyle = style({
  fontSize: '1.125rem',
  fontWeight: '500',
  color: '#111827',
});

// Confirmation text (replaces mt-2 text-sm text-gray-600)
export const confirmationTextStyle = style({
  marginTop: '0.5rem',
  fontSize: '0.875rem',
  color: '#4b5563',
});

// Email highlight (replaces font-medium)
export const emailHighlightStyle = style({
  fontWeight: '500',
});

// Button wrapper (replaces mt-6)
export const buttonWrapperStyle = style({
  marginTop: '1.5rem',
});

// Go to login link (replaces btn btn-primary w-full flex justify-center)
export const goToLoginLinkStyle = style({
  padding: '0.5rem 1rem',
  backgroundColor: '#3b82f6',
  color: 'white',
  borderRadius: '0.375rem',
  width: '100%',
  display: 'flex',
  justifyContent: 'center',
  textDecoration: 'none',
});

// Different email button wrapper (replaces mt-4 text-center)
export const differentEmailWrapperStyle = style({
  marginTop: '1rem',
  textAlign: 'center',
});

// Different email button (replaces text-sm text-primary-600 hover:text-primary-500)
export const differentEmailButtonStyle = style({
  fontSize: '0.875rem',
  color: '#2563eb',
  ':hover': {
    color: '#3b82f6',
  },
  background: 'none',
  border: 'none',
  cursor: 'pointer',
});

export const container = style({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  minHeight: '100vh',
  padding: '20px',
  backgroundColor: '#f5f5f5'
});

export const card = style({
  backgroundColor: 'white',
  borderRadius: '8px',
  padding: '2rem',
  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
  width: '100%',
  maxWidth: '400px'
});

export const form = style({
  display: 'flex',
  flexDirection: 'column',
  gap: '1rem'
});

export const formGroup = style({
  display: 'flex',
  flexDirection: 'column',
  gap: '0.5rem'
});

export const input = style({
  padding: '0.75rem',
  borderRadius: '4px',
  border: '1px solid #ddd',
  fontSize: '1rem',
  selectors: {
    '&:focus': {
      outline: 'none',
      borderColor: '#0070f3'
    }
  }
});

export const submitButton = style({
  padding: '0.75rem',
  backgroundColor: '#0070f3',
  color: 'white',
  border: 'none',
  borderRadius: '4px',
  fontSize: '1rem',
  cursor: 'pointer',
  selectors: {
    '&:disabled': {
      backgroundColor: '#ccc',
      cursor: 'not-allowed'
    },
    '&:hover:not(:disabled)': {
      backgroundColor: '#0060df'
    }
  }
});

export const error = style({
  color: '#dc2626',
  fontSize: '0.875rem',
  marginTop: '0.5rem'
});

export const links = style({
  marginTop: '1rem',
  textAlign: 'center',
  fontSize: '0.875rem',
  color: '#666',
  selectors: {
    '& a': {
      color: '#0070f3',
      textDecoration: 'none'
    },
    '& a:hover': {
      textDecoration: 'underline'
    }
  }
});

export const confirmation = style({
  textAlign: 'center',
  padding: '2rem 0'
});

export const loginLink = style({
  display: 'inline-block',
  marginTop: '1rem',
  color: '#0070f3',
  textDecoration: 'none',
  selectors: {
    '&:hover': {
      textDecoration: 'underline'
    }
  }
});