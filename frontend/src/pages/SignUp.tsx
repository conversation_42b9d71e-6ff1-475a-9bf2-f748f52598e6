import { Navigate } from 'react-router-dom';
import { SignUp, useAuth } from '@clerk/clerk-react';
import * as styles from './Login.css'; // Reuse the login styles

const SignUpPage: React.FC = () => {
  const { isLoaded, isSignedIn } = useAuth();

  // Redirect if already logged in
  if (isLoaded && isSignedIn) {
    return <Navigate to="/" replace />;
  }

  return (
    <div className={styles.container}>
      <div className={styles.card}>
        <h1>Sign Up for Rolewise.ai</h1>
        <div className={styles.clerkContainer}>
          <SignUp 
            routing="path" 
            path="/signup" 
            signInUrl="/login"
            redirectUrl="/"
          />
        </div>
      </div>
    </div>
  );
};

export default SignUpPage;
