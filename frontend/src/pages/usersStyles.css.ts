import { style } from '@vanilla-extract/css';

// Existing styles
export const usersContainer = style({
  padding: '1.5rem',
  backgroundColor: 'white',
});

export const titleStyle = style({
  fontSize: '1.5rem',
  fontWeight: 'bold',
  color: '#1f2937', // Equivalent to text-gray-900
});

export const buttonStyle = style({
  padding: '0.5rem 1rem',
  backgroundColor: '#3b82f6', // Equivalent to bg-primary-600
  color: 'white',
  borderRadius: '0.375rem',
  cursor: 'pointer',
});

export const inputStyle = style({
  padding: '0.5rem',
  borderRadius: '0.375rem',
  border: '1px solid #d1d5db', // Equivalent to border-gray-300
  width: '100%',
});

export const loadingStyle = style({
  display: 'flex',
  justifyContent: 'center',
  padding: '3rem',
});

export const errorStyle = style({
  backgroundColor: '#fee2e2', // Equivalent to bg-red-50
  borderLeft: '4px solid #f87171', // Equivalent to border-red-500
  padding: '1rem',
});

// New styles to replace Tailwind classes
// Header (replaces flex justify-between items-center)
export const headerStyle = style({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
});

// Search section (replaces flex justify-between items-center)
export const searchSectionStyle = style({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
});

// Table container (replaces overflow-x-auto)
export const tableContainerStyle = style({
  overflowX: 'auto',
});

// Table (replaces min-w-full divide-y divide-gray-200)
export const tableStyle = style({
  minWidth: '100%',
  borderCollapse: 'collapse',
});

// Table head (replaces bg-gray-50)
export const tableHeadStyle = style({
  backgroundColor: '#f9fafb',
  borderBottom: '1px solid #e5e7eb',
});

// Table header cell (replaces px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider)
export const tableHeaderCellStyle = style({
  paddingLeft: '1.5rem',
  paddingRight: '1.5rem',
  paddingTop: '0.75rem',
  paddingBottom: '0.75rem',
  textAlign: 'left',
  fontSize: '0.75rem',
  fontWeight: '500',
  color: '#6b7280',
  textTransform: 'uppercase',
  letterSpacing: '0.05em',
});

// Table header cell for actions (replaces relative px-6 py-3)
export const tableHeaderActionsCellStyle = style({
  position: 'relative',
  paddingLeft: '1.5rem',
  paddingRight: '1.5rem',
  paddingTop: '0.75rem',
  paddingBottom: '0.75rem',
});

// Screen-reader-only (replaces sr-only)
export const srOnlyStyle = style({
  position: 'absolute',
  width: '1px',
  height: '1px',
  padding: 0,
  margin: '-1px',
  overflow: 'hidden',
  clip: 'rect(0, 0, 0, 0)',
  whiteSpace: 'nowrap',
  borderWidth: 0,
});

// Table body (replaces bg-white divide-y divide-gray-200)
export const tableBodyStyle = style({
  backgroundColor: 'white',
  '& > tr': {
    borderBottom: '1px solid #e5e7eb',
  },
});

// No users cell (replaces px-6 py-4 text-center text-gray-500)
export const noUsersCellStyle = style({
  paddingLeft: '1.5rem',
  paddingRight: '1.5rem',
  paddingTop: '1rem',
  paddingBottom: '1rem',
  textAlign: 'center',
  color: '#6b7280',
});

// Table cell (replaces px-6 py-4 whitespace-nowrap)
export const tableCellStyle = style({
  paddingLeft: '1.5rem',
  paddingRight: '1.5rem',
  paddingTop: '1rem',
  paddingBottom: '1rem',
  whiteSpace: 'nowrap',
});

// Table cell for created date (replaces px-6 py-4 whitespace-nowrap text-sm text-gray-500)
export const tableCellDateStyle = style({
  paddingLeft: '1.5rem',
  paddingRight: '1.5rem',
  paddingTop: '1rem',
  paddingBottom: '1rem',
  whiteSpace: 'nowrap',
  fontSize: '0.875rem',
  color: '#6b7280',
});

// User name (replaces text-sm font-medium text-gray-900)
export const userNameStyle = style({
  fontSize: '0.875rem',
  fontWeight: '500',
  color: '#111827',
});

// User email (replaces text-sm text-gray-500)
export const userEmailStyle = style({
  fontSize: '0.875rem',
  color: '#6b7280',
});

// Role badge (replaces px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800)
export const roleBadgeStyle = style({
  paddingLeft: '0.5rem',
  paddingRight: '0.5rem',
  display: 'inline-flex',
  fontSize: '0.75rem',
  lineHeight: '1.25rem',
  fontWeight: '600',
  borderRadius: '9999px',
  backgroundColor: '#dcfce7',
  color: '#166534',
});

// Actions cell (replaces px-6 py-4 whitespace-nowrap text-right text-sm font-medium)
export const actionsCellStyle = style({
  paddingLeft: '1.5rem',
  paddingRight: '1.5rem',
  paddingTop: '1rem',
  paddingBottom: '1rem',
  whiteSpace: 'nowrap',
  textAlign: 'right',
  fontSize: '0.875rem',
  fontWeight: '500',
});

// Edit button (replaces text-primary-600 hover:text-primary-900 mr-4)
export const editButtonStyle = style({
  color: '#2563eb',
  ':hover': {
    color: '#1e3a8a',
  },
  marginRight: '1rem',
  background: 'none',
  border: 'none',
  cursor: 'pointer',
});

// Delete button (replaces text-red-600 hover:text-red-900)
export const deleteButtonStyle = style({
  color: '#dc2626',
  ':hover': {
    color: '#991b1b',
  },
  background: 'none',
  border: 'none',
  cursor: 'pointer',
});

export const container = style({
  padding: '2rem',
  maxWidth: '1200px',
  margin: '0 auto'
});

export const header = style({
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  marginBottom: '2rem'
});

export const filters = style({
  display: 'flex',
  gap: '1rem',
  alignItems: 'center'
});

export const searchInput = style({
  padding: '0.75rem',
  borderRadius: '4px',
  border: '1px solid #ddd',
  fontSize: '1rem',
  minWidth: '250px',
  selectors: {
    '&:focus': {
      outline: 'none',
      borderColor: '#0070f3'
    }
  }
});

export const roleFilter = style({
  padding: '0.75rem',
  borderRadius: '4px',
  border: '1px solid #ddd',
  fontSize: '1rem',
  backgroundColor: 'white',
  selectors: {
    '&:focus': {
      outline: 'none',
      borderColor: '#0070f3'
    }
  }
});

export const userGrid = style({
  display: 'grid',
  gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))',
  gap: '1.5rem'
});

export const userCard = style({
  backgroundColor: 'white',
  borderRadius: '8px',
  padding: '1.5rem',
  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
  display: 'flex',
  flexDirection: 'column',
  gap: '0.5rem'
});

export const userName = style({
  fontSize: '1.25rem',
  fontWeight: '600',
  color: '#111'
});

export const userEmail = style({
  color: '#666',
  fontSize: '0.875rem'
});

export const userRole = style({
  color: '#0070f3',
  fontSize: '0.875rem',
  fontWeight: '500'
});

export const userCreated = style({
  color: '#666',
  fontSize: '0.75rem',
  marginTop: '0.5rem'
});

export const loading = style({
  textAlign: 'center',
  padding: '2rem',
  color: '#666'
});

export const error = style({
  color: '#dc2626',
  backgroundColor: '#fee2e2',
  padding: '1rem',
  borderRadius: '4px',
  marginBottom: '1rem'
});

export const noResults = style({
  textAlign: 'center',
  padding: '2rem',
  color: '#666',
  backgroundColor: '#f9fafb',
  borderRadius: '8px'
});