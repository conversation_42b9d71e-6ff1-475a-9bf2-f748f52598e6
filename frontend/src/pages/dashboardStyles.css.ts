import { style } from '@vanilla-extract/css';

// Test style to verify Vanilla Extract processing
export const testStyle = style({
  color: 'red',
});

// Base container - reduce padding on mobile
export const dashboardContainer = style({
  padding: '1rem',
  paddingTop: '5rem', // Clear navbar (4rem) + extra space
  backgroundColor: '#f9fafb',
  minHeight: '100vh',
  '@media': {
    'screen and (min-width: 768px)': {
      padding: '1.5rem',
      paddingLeft: 'calc(4rem + 1.5rem)', // Collapsed sidebar
      paddingTop: '5rem', // Keep consistent with navbar
    },
    'screen and (min-width: 1024px)': {
      paddingLeft: 'calc(16rem + 1.5rem)', // Expanded sidebar
    },
  },
});

// Error message style
export const errorMessageStyle = style({
  padding: '1.5rem',
  margin: '1rem 0',
  backgroundColor: '#fee2e2',
  border: '1px solid #ef4444',
  borderRadius: '0.375rem',
  color: '#991b1b',
});

// Title - slightly smaller on mobile
export const titleStyle = style({
  fontSize: '1.25rem', // Reduced from 1.5rem for mobile
  fontWeight: 'bold',
  color: '#1f2937',
  '@media': {
    'screen and (min-width: 768px)': {
      fontSize: '1.5rem', // Original size for larger screens
    },
  },
});

// Card - reduce padding and ensure full width on mobile
export const cardStyle = style({
  padding: '0.75rem', // Reduced from 1rem
  borderRadius: '0.375rem',
  boxShadow: '0 1px 2px rgba(0, 0, 0, 0.1)',
  backgroundColor: 'white',
  width: '100%', // Ensure cards take full width on mobile
  '@media': {
    'screen and (min-width: 768px)': {
      padding: '1rem', // Restore original padding
    },
  },
});

// Header - keep simple and compact
export const headerStyle = style({
  display: 'flex',
  justifyContent: 'flex-start',
  alignItems: 'center',
  marginBottom: '1rem', // Reduced from 1.5rem
  '@media': {
    'screen and (min-width: 768px)': {
      marginBottom: '1.5rem',
    },
  },
});

// Content section spacing
export const contentSectionStyle = style({
  marginBottom: '1rem', // Reduced from 1.5rem
  '@media': {
    'screen and (min-width: 768px)': {
      marginBottom: '1.5rem',
    },
  },
});

// Section title - smaller on mobile
export const sectionTitleStyle = style({
  fontSize: '1rem', // Reduced from 1.125rem
  fontWeight: '600',
  marginBottom: '0.75rem', // Reduced from 1rem
  '@media': {
    'screen and (min-width: 768px)': {
      fontSize: '1.125rem',
      marginBottom: '1rem',
    },
  },
});

export const insightsListStyle = style({
  display: 'flex',
  flexDirection: 'column',
  gap: '0.375rem', // Reduced from 0.5rem
  color: '#4b5563',
  fontSize: '0.875rem', // Explicitly set
  '@media': {
    'screen and (min-width: 768px)': {
      gap: '0.5rem',
    },
  },
});

export const insightItemStyle = style({
  display: 'flex',
  alignItems: 'flex-start',
  flexWrap: 'wrap', // Allow wrapping
  gap: '0.25rem', // Add gap for wrapped content
});

// Insight icons - no major size change needed, just spacing
export const insightIconWarningStyle = style({
  color: '#eab308',
  marginRight: '0.5rem',
});

export const insightIconSuccessStyle = style({
  color: '#22c55e',
  marginRight: '0.5rem',
});

export const insightIconInfoStyle = style({
  color: '#3b82f6',
  marginRight: '0.5rem',
});

// Processing status styles
export const processingStatusStyle = style({
  display: 'flex',
  flexDirection: 'column',
  gap: '0.5rem',
  margin: '1rem 0',
  padding: '0.75rem',
  backgroundColor: '#f3f4f6',
  borderRadius: '0.375rem',
  fontSize: '0.875rem',
});

export const statusBadgeStyle = style({
  display: 'inline-flex',
  alignItems: 'center',
  padding: '0.25rem 0.5rem',
  borderRadius: '9999px',
  fontWeight: '500',
  fontSize: '0.75rem',
  textTransform: 'uppercase',
  marginRight: '0.5rem',
});

export const statusPendingStyle = style([
  statusBadgeStyle,
  {
    backgroundColor: '#e5e7eb',
    color: '#4b5563',
  },
]);

export const statusFetchingStyle = style([
  statusBadgeStyle,
  {
    backgroundColor: '#dbeafe',
    color: '#1e40af',
  },
]);

export const statusProcessingStyle = style([
  statusBadgeStyle,
  {
    backgroundColor: '#fef3c7',
    color: '#92400e',
  },
]);

export const statusInferringStyle = style([
  statusBadgeStyle,
  {
    backgroundColor: '#d1fae5',
    color: '#065f46',
  },
]);

export const statusCompletedStyle = style([
  statusBadgeStyle,
  {
    backgroundColor: '#dcfce7',
    color: '#166534',
  },
]);

export const statusFailedStyle = style([
  statusBadgeStyle,
  {
    backgroundColor: '#fee2e2',
    color: '#991b1b',
  },
]);