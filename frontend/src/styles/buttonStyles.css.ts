import { style } from '@vanilla-extract/css';

export const buttonStyle = style({
  padding: '0.5rem 1rem',
  borderRadius: '0.375rem',
  fontWeight: 500,
  transition: 'all 0.2s',
  cursor: 'pointer',
  border: 'none',
  backgroundColor: 'transparent',
  ':focus': {
    outline: '2px solid #3b82f6', // Equivalent to focus:ring-2 focus:ring-primary-500
    outlineOffset: '2px',
  },
});

export const primaryButtonStyle = style({
  backgroundColor: '#3b82f6', // Equivalent to bg-primary-600
  color: 'white',
  ':hover': {
    backgroundColor: '#2563eb', // Equivalent to hover:bg-primary-700
  },
  ':disabled': {
    backgroundColor: '#93c5fd', // Equivalent to a lighter shade (e.g., bg-primary-300)
    cursor: 'not-allowed',
    opacity: 0.7,
  },
});

export const outlineButtonStyle = style({
  border: '1px solid #d1d5db', // Equivalent to border-gray-300
  backgroundColor: 'white',
  color: '#374151', // Equivalent to text-gray-700
  ':hover': {
    backgroundColor: '#f3f4f6', // Equivalent to hover:bg-gray-50
  },
  ':disabled': {
    borderColor: '#e5e7eb', // Equivalent to border-gray-200
    color: '#6b7280', // Equivalent to text-gray-500
    cursor: 'not-allowed',
    opacity: 0.7,
  },
});