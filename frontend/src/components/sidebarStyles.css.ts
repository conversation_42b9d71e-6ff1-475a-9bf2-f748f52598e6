import { style, globalStyle } from '@vanilla-extract/css';

// Global reset
globalStyle('*, *:before, *:after', {
  boxSizing: 'border-box',
  margin: 0,
  padding: 0,
});

globalStyle('html, body', {
  height: '100%',
  fontFamily: 'system-ui, sans-serif',
});

// Sidebar base
export const sidebarBaseStyle = style({
  backgroundColor: 'white',
  borderRight: '1px solid #e5e7eb',
  transition: 'width 0.3s ease, transform 0.3s ease', // Transition both width and transform
  position: 'fixed',
  top: '4rem', // Below the navbar
  height: 'calc(100vh - 4rem)',
  left: 0,
  zIndex: 1000,
  paddingTop: 0,
  overflow: 'hidden',
  display: 'flex',
  flexDirection: 'column',
  '@media': {
    'screen and (min-width: 768px)': {
      position: 'sticky',
    },
  },
});

// Open state
export const sidebarOpenStyle = style({
  width: '16rem',
  transform: 'translateX(0)',
});

// Closed state
export const sidebarClosedStyle = style({
  width: '0', // Collapse width on desktop
  transform: 'translateX(-100%)', // Slide out on mobile
  '@media': {
    'screen and (min-width: 768px)': {
      width: '4rem', // Icon-only width on desktop when closed
      transform: 'translateX(0)', // Stay visible
    },
  },
});

// Sidebar content
export const sidebarContentStyle = style({
  paddingLeft: '0.75rem',
  paddingRight: '0.75rem',
  paddingTop: '1rem',
  paddingBottom: '1rem',
  overflowY: 'auto',
  flex: 1,
  width: '16rem', // Fixed width to prevent content collapse
});

// Nav list
export const navListStyle = style({
  marginTop: '0.5rem',
  display: 'flex',
  flexDirection: 'column',
  gap: '0.5rem',
});

// Toggle button inside sidebar
export const toggleButtonStyle = style({
  width: '100%',
  display: 'flex',
  justifyContent: 'flex-end',
  padding: '0.75rem',
  color: '#6b7280',
  backgroundColor: 'transparent',
  border: 'none',
  ':hover': {
    backgroundColor: '#f3f4f6',
  },
  borderRadius: '0.375rem',
  cursor: 'pointer',
  fontSize: '1.5rem',
});

// Hamburger button
export const sidebarToggleIndicatorStyle = style({
  position: 'fixed',
  top: '1rem', // Center vertically in the 4rem navbar
  left: '0.5rem',
  zIndex: 1100,
  padding: '0.5rem',
  fontSize: '1.5rem',
  color: '#6b7280',
  backgroundColor: 'white',
  border: '1px solid #e5e7eb',
  borderRadius: '0.375rem',
  cursor: 'pointer',
  ':hover': {
    backgroundColor: '#f3f4f6',
  },
  '@media': {
    'screen and (min-width: 768px)': {
      display: 'none',
    },
  },
});

// Nav item base
export const navItemBaseStyle = style({
  display: 'flex',
  alignItems: 'center',
  padding: '0.5rem',
  borderRadius: '0.375rem',
  transition: 'all 0.3s ease',
  ':hover': {
    backgroundColor: '#f3f4f6',
  },
  width: '100%',
  gap: '0.75rem',
  textDecoration: 'none',
  overflow: 'hidden', // Prevent text overflow
});

// Icon
export const iconStyle = style({
  fontSize: '1.25rem',
  minWidth: '1.25rem',
});

// Nav item text - hide when sidebar is closed
export const navItemTextStyle = style({
  marginLeft: '0.75rem',
  whiteSpace: 'nowrap',
  display: 'inline', // Default display
  selectors: {
    [`${sidebarClosedStyle} &`]: {
      display: 'none', // Hide text when sidebar is closed
    },
    [`${sidebarOpenStyle} &`]: {
      display: 'inline', // Show text when sidebar is open
    },
  },
});

// Active/inactive states
export const navItemActiveStyle = style({
  backgroundColor: '#dbeafe',
  color: '#1e3a8a',
});

export const navItemInactiveStyle = style({
  backgroundColor: 'transparent',
  color: '#111827',
});

export const container = style({
  width: '250px',
  height: '100vh',
  backgroundColor: '#1a1a1a',
  color: 'white',
  padding: '1rem',
  display: 'flex',
  flexDirection: 'column',
});

export const logo = style({
  padding: '1rem',
  marginBottom: '2rem',
  borderBottom: '1px solid #333',
});

export const nav = style({
  display: 'flex',
  flexDirection: 'column',
  gap: '0.5rem',
});

export const navItem = style({
  padding: '0.75rem 1rem',
  borderRadius: '0.375rem',
  color: '#e5e7eb',
  textDecoration: 'none',
  transition: 'all 0.2s ease-in-out',
  ':hover': {
    backgroundColor: '#333',
    color: 'white',
  },
});

export const active = style({
  backgroundColor: '#3b82f6',
  color: 'white',
  ':hover': {
    backgroundColor: '#2563eb',
  },
});