import { memo } from 'react';
import { Handle, Position, NodeProps } from 'reactflow';
import { SupabaseGroup } from '../../types/supabase';
import * as styles from './roleGraphStyles.css';

interface GroupNodeData {
  label: string;
  description: string;
  group: SupabaseGroup;
}

export const GroupNode = memo(({ data }: NodeProps<GroupNodeData>) => {
  const { label, description, group } = data;
  
  return (
    <div className={styles.groupNode}>
      <div className={styles.nodeHeader}>
        <div className={styles.nodeTitle}>{label}</div>
      </div>
      {description && (
        <div className={styles.nodeDescription}>{description}</div>
      )}
      <Handle type="source" position={Position.Right} />
    </div>
  );
});

GroupNode.displayName = 'GroupNode';
