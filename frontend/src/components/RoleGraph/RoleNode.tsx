import { memo } from 'react';
import { Handle, Position, NodeProps } from 'reactflow';
import { RolewiseRole } from '../../types/rolewise';
import * as styles from './roleGraphStyles.css';

interface RoleNodeData {
  label: string;
  description: string;
  role: RolewiseRole;
  selected?: boolean;
}

export const RoleNode = memo(({ data }: NodeProps<RoleNodeData>) => {
  const { label, description, role, selected } = data;
  const confidenceScore = role.confidenceScore || 0;
  
  // Format confidence score as percentage
  const confidencePercentage = (confidenceScore * 100).toFixed(0);
  
  // Determine confidence color
  const getConfidenceColor = () => {
    if (confidenceScore >= 0.8) return '#10b981'; // Green
    if (confidenceScore >= 0.6) return '#f59e0b'; // Yellow
    return '#ef4444'; // Red
  };
  
  return (
    <div className={`${styles.roleNode} ${selected ? styles.selectedNode : ''}`}>
      <Handle type="target" position={Position.Left} />
      <div className={styles.nodeHeader}>
        <div className={styles.nodeTitle}>{label}</div>
        {confidenceScore > 0 && (
          <div 
            className={styles.confidenceBadge}
            style={{ backgroundColor: getConfidenceColor() }}
          >
            {confidencePercentage}%
          </div>
        )}
      </div>
      <div className={styles.nodeDescription}>{description}</div>
      {role.source && (
        <div className={styles.nodeSource}>
          Source: {role.source}
        </div>
      )}
      <Handle type="source" position={Position.Right} />
    </div>
  );
});

RoleNode.displayName = 'RoleNode';
