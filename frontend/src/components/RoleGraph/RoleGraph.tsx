import { useCallback, useEffect, useState } from 'react';
import <PERSON>act<PERSON><PERSON>, {
  Node,
  Edge,
  Controls,
  Background,
  MiniMap,
  useNodesState,
  useEdgesState,
  NodeTypes,
  EdgeTypes,
  Panel,
  MarkerType,
} from 'reactflow';
import 'reactflow/dist/style.css';
import { useRolewiseStore } from '../../stores/rolewiseStore';
import { RoleNode } from './RoleNode';
import { GroupNode } from './GroupNode';
import { ApplicationNode } from './ApplicationNode';
import * as styles from './roleGraphStyles.css';

// Define custom node types
const nodeTypes: NodeTypes = {
  role: RoleNode,
  group: GroupNode,
  application: ApplicationNode,
};

interface RoleGraphProps {
  selectedRoleId?: string | null;
  onRoleSelect?: (roleId: string) => void;
}

const RoleGraph = ({ selectedRoleId, onRoleSelect }: RoleGraphProps) => {
  const { roles, groups, applications, roleApplicationMappings, loading } = useRolewiseStore();
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [layouting, setLayouting] = useState(false);

  // Generate nodes and edges from the data
  const generateGraph = useCallback(() => {
    if (loading) return;
    
    setLayouting(true);
    
    const newNodes: Node[] = [];
    const newEdges: Edge[] = [];
    
    // Add role nodes
    roles.forEach((role, index) => {
      newNodes.push({
        id: `role-${role.id}`,
        type: 'role',
        data: { 
          label: role.name,
          description: role.description,
          role,
          selected: role.id === selectedRoleId,
        },
        position: { x: 0, y: index * 150 },
        style: {
          width: 200,
          borderColor: role.id === selectedRoleId ? '#3b82f6' : undefined,
          borderWidth: role.id === selectedRoleId ? 2 : undefined,
          boxShadow: role.id === selectedRoleId ? '0 0 10px rgba(59, 130, 246, 0.5)' : undefined,
        },
      });
    });
    
    // Add group nodes
    groups.forEach((group, index) => {
      newNodes.push({
        id: `group-${group.id}`,
        type: 'group',
        data: { 
          label: group.name,
          description: group.description || '',
          group,
        },
        position: { x: -300, y: index * 100 },
      });
      
      // Connect groups to roles based on metadata
      roles.forEach(role => {
        if (role.metadata?.topGroups && Array.isArray(role.metadata.topGroups)) {
          const topGroups = role.metadata.topGroups as string[];
          if (topGroups.includes(group.name)) {
            newEdges.push({
              id: `edge-group-${group.id}-role-${role.id}`,
              source: `group-${group.id}`,
              target: `role-${role.id}`,
              markerEnd: {
                type: MarkerType.ArrowClosed,
              },
              style: { stroke: '#6b7280' },
            });
          }
        }
      });
    });
    
    // Add application nodes
    applications.forEach((app, index) => {
      newNodes.push({
        id: `app-${app.id}`,
        type: 'application',
        data: { 
          label: app.name,
          description: app.description || '',
          application: app,
        },
        position: { x: 300, y: index * 100 },
      });
    });
    
    // Connect roles to applications based on mappings
    roleApplicationMappings.forEach(mapping => {
      newEdges.push({
        id: `edge-role-${mapping.role_id}-app-${mapping.application_id}`,
        source: `role-${mapping.role_id}`,
        target: `app-${mapping.application_id}`,
        markerEnd: {
          type: MarkerType.ArrowClosed,
        },
        style: { stroke: '#3b82f6' },
        data: {
          confidence: mapping.confidence_score,
        },
        label: mapping.confidence_score ? 
          `${(mapping.confidence_score * 100).toFixed(0)}%` : '',
        labelStyle: { fill: '#3b82f6', fontWeight: 'bold' },
        labelBgStyle: { fill: 'white', fillOpacity: 0.8 },
      });
    });
    
    setNodes(newNodes);
    setEdges(newEdges);
    
    // Apply automatic layout
    setTimeout(() => {
      setLayouting(false);
    }, 100);
  }, [roles, groups, applications, roleApplicationMappings, loading, selectedRoleId, setNodes, setEdges]);
  
  // Generate the graph when data changes
  useEffect(() => {
    generateGraph();
  }, [generateGraph]);
  
  // Handle node click
  const onNodeClick = useCallback((event: React.MouseEvent, node: Node) => {
    if (node.type === 'role' && onRoleSelect) {
      const roleId = node.id.replace('role-', '');
      onRoleSelect(roleId);
    }
  }, [onRoleSelect]);
  
  if (loading) {
    return <div className={styles.loadingContainer}>Loading role relationships...</div>;
  }
  
  return (
    <div className={styles.graphContainer}>
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onNodeClick={onNodeClick}
        nodeTypes={nodeTypes}
        fitView
        attributionPosition="bottom-right"
      >
        <Controls />
        <MiniMap />
        <Background />
        <Panel position="top-right">
          <button 
            onClick={generateGraph}
            className={styles.layoutButton}
            disabled={layouting}
          >
            {layouting ? 'Applying Layout...' : 'Reset Layout'}
          </button>
        </Panel>
      </ReactFlow>
    </div>
  );
};

export default RoleGraph;
