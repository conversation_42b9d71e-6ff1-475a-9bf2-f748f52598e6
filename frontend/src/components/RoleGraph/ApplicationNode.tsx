import { memo } from 'react';
import { <PERSON><PERSON>, Position, NodeProps } from 'reactflow';
import { SupabaseApplication } from '../../types/supabase';
import * as styles from './roleGraphStyles.css';

interface ApplicationNodeData {
  label: string;
  description: string;
  application: SupabaseApplication;
}

export const ApplicationNode = memo(({ data }: NodeProps<ApplicationNodeData>) => {
  const { label, description, application } = data;
  
  return (
    <div className={styles.applicationNode}>
      <Handle type="target" position={Position.Left} />
      <div className={styles.nodeHeader}>
        <div className={styles.nodeTitle}>{label}</div>
      </div>
      {description && (
        <div className={styles.nodeDescription}>{description}</div>
      )}
    </div>
  );
});

ApplicationNode.displayName = 'ApplicationNode';
