import { style } from '@vanilla-extract/css';

export const navbarStyle = style({
  backgroundColor: 'white',
  borderBottom: '1px solid #e5e7eb',
  padding: '0.625rem 1rem',
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  height: '4rem',
  width: '100%',
  position: 'relative',
  zIndex: 50,
});

export const navbarLeftStyle = style({
  display: 'flex',
  alignItems: 'center',
  gap: '1rem',
});

export const navbarRightStyle = style({
  display: 'flex',
  alignItems: 'center',
  gap: '1rem',
});

export const logoContainerStyle = style({
  width: '2rem',
  height: '2rem',
  backgroundColor: '#3b82f6',
  borderRadius: '0.375rem',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
});

export const logoTextStyle = style({
  color: 'white',
  fontSize: '1.25rem',
  fontWeight: '600',
});

export const userNameStyle = style({
  fontSize: '1.25rem',
  fontWeight: '600',
  whiteSpace: 'nowrap',
  color: '#3b82f6',
  display: 'none',
  '@media': {
    'screen and (min-width: 768px)': {
      display: 'inline',
    },
  },
});

export const profileButtonStyle = style({
  display: 'flex',
  alignItems: 'center',
  gap: '0.5rem',
  padding: '0.5rem',
  borderRadius: '0.375rem',
  ':hover': {
    backgroundColor: '#f3f4f6',
  },
  ':focus': {
    outline: '2px solid #3b82f6',
    outlineOffset: '2px',
  },
});

export const profileAvatarStyle = style({
  width: '2rem',
  height: '2rem',
  backgroundColor: '#3b82f6',
  borderRadius: '9999px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  color: 'white',
  fontSize: '0.875rem',
  fontWeight: '500',
});

export const emailTextStyle = style({
  fontSize: '0.875rem',
  color: '#374151',
  display: 'none',
  '@media': {
    'screen and (min-width: 768px)': {
      display: 'inline',
    },
  },
});

export const profileMenuStyle = style({
  position: 'absolute',
  right: 0,
  top: 'calc(100% + 0.5rem)',
  width: '12rem',
  backgroundColor: 'white',
  borderRadius: '0.375rem',
  boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
  padding: '0.25rem 0',
  zIndex: 100,
  border: '1px solid #e5e7eb',
});

export const signOutButtonStyle = style({
  width: '100%',
  padding: '0.75rem 1rem',
  textAlign: 'left',
  color: '#374151',
  fontSize: '0.875rem',
  fontWeight: '500',
  cursor: 'pointer',
  ':hover': {
    backgroundColor: '#f3f4f6',
  },
  ':focus': {
    outline: 'none',
    backgroundColor: '#f3f4f6',
  },
});

export const srOnlyStyle = style({
  position: 'absolute',
  width: '1px',
  height: '1px',
  padding: 0,
  margin: '-1px',
  overflow: 'hidden',
  clip: 'rect(0, 0, 0, 0)',
  whiteSpace: 'nowrap',
  border: 0,
});