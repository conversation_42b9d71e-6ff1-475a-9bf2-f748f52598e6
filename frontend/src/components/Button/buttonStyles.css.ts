import { style } from '@vanilla-extract/css';

export const button = style({
  display: 'inline-flex',
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: '0.375rem',
  fontWeight: 500,
  transition: 'all 0.2s',
  cursor: 'pointer',
  border: 'none',
  outline: 'none',
  ':focus': {
    boxShadow: '0 0 0 3px rgba(59, 130, 246, 0.5)',
  },
  ':disabled': {
    opacity: 0.7,
    cursor: 'not-allowed',
  },
});

export const primary = style({
  backgroundColor: '#3b82f6', // blue-500
  color: 'white',
  ':hover:not(:disabled)': {
    backgroundColor: '#2563eb', // blue-600
  },
});

export const secondary = style({
  backgroundColor: '#f3f4f6', // gray-100
  color: '#1f2937', // gray-800
  ':hover:not(:disabled)': {
    backgroundColor: '#e5e7eb', // gray-200
  },
});

export const danger = style({
  backgroundColor: '#ef4444', // red-500
  color: 'white',
  ':hover:not(:disabled)': {
    backgroundColor: '#dc2626', // red-600
  },
});

export const small = style({
  fontSize: '0.875rem',
  padding: '0.375rem 0.75rem',
});

export const medium = style({
  fontSize: '1rem',
  padding: '0.5rem 1rem',
});

export const large = style({
  fontSize: '1.125rem',
  padding: '0.625rem 1.25rem',
});

export const fullWidth = style({
  width: '100%',
});
