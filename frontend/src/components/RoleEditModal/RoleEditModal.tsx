import React, { useState, useEffect } from 'react';
import { RolewiseRole } from '../../types/rolewise';
import * as styles from './roleEditModalStyles.css';

interface RoleEditModalProps {
  role: RolewiseRole | null;
  isOpen: boolean;
  onClose: () => void;
  onSave: (updatedRole: RolewiseRole, feedback?: string) => Promise<void>;
}

const RoleEditModal: React.FC<RoleEditModalProps> = ({ role, isOpen, onClose, onSave }) => {
  const [formData, setFormData] = useState<RolewiseRole | null>(null);
  const [feedback, setFeedback] = useState<string>('');
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Reset form when role changes
  useEffect(() => {
    if (role) {
      setFormData({ ...role });
      setFeedback('');
      setError(null);
    } else {
      setFormData(null);
    }
  }, [role]);

  if (!isOpen || !formData) {
    return null;
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => {
      if (!prev) return null;
      return { ...prev, [name]: value };
    });
  };

  const handleConfidenceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value) / 100;
    setFormData((prev) => {
      if (!prev) return null;
      return { ...prev, confidenceScore: value };
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData) return;

    setIsSaving(true);
    setError(null);

    try {
      // Mark the role as manually adjusted
      const updatedRole = {
        ...formData,
        source: 'manual',
      };

      await onSave(updatedRole, feedback.trim() || undefined);
      onClose();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while saving the role');
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modalContent}>
        <div className={styles.modalHeader}>
          <h2 className={styles.modalTitle}>Edit Role</h2>
          <button className={styles.closeButton} onClick={onClose}>
            &times;
          </button>
        </div>

        <form onSubmit={handleSubmit} className={styles.modalBody}>
          {error && <div className={styles.errorMessage}>{error}</div>}

          <div className={styles.formGroup}>
            <label htmlFor="name" className={styles.formLabel}>
              Role Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className={styles.formInput}
              required
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="description" className={styles.formLabel}>
              Description
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              className={styles.formTextarea}
              rows={3}
              required
            />
          </div>

          {formData.confidenceScore !== undefined && (
            <div className={styles.formGroup}>
              <label htmlFor="confidenceScore" className={styles.formLabel}>
                Confidence Score: {formData.confidenceScore !== undefined ? Math.round(formData.confidenceScore * 100) : 0}%
              </label>
              <input
                type="range"
                id="confidenceScore"
                name="confidenceScore"
                min="0"
                max="100"
                value={formData.confidenceScore !== undefined ? Math.round(formData.confidenceScore * 100) : 0}
                onChange={handleConfidenceChange}
                className={styles.formRange}
              />
            </div>
          )}

          <div className={styles.formGroup}>
            <label htmlFor="feedback" className={styles.formLabel}>
              Feedback (Optional)
            </label>
            <textarea
              id="feedback"
              name="feedback"
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              className={styles.formTextarea}
              rows={3}
              placeholder="Provide feedback on why you're adjusting this role to help improve our inference algorithm"
            />
          </div>

          <div className={styles.modalFooter}>
            <button
              type="button"
              onClick={onClose}
              className={styles.cancelButton}
              disabled={isSaving}
            >
              Cancel
            </button>
            <button
              type="submit"
              className={styles.saveButton}
              disabled={isSaving}
            >
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default RoleEditModal;
