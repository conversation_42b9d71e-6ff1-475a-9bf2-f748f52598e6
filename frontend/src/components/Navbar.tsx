import { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useUser, UserButton } from '@clerk/clerk-react';
import * as styles from './navbarStyles.css.ts';

const Navbar = () => {
  const { isLoaded, isSignedIn, user } = useUser();
  const navigate = useNavigate(); // Hook for navigation

  return (
    <nav className={styles.navbarStyle}>
      <div className={styles.navbarLeftStyle}>
        <div className={styles.logoContainerStyle}>
          <span className={styles.logoTextStyle}>R</span>
        </div>
        <span className={styles.userNameStyle}>Rolewise.ai</span>
      </div>

      <div className={styles.navbarRightStyle}>
        <div className={styles.userButtonContainer}>
          {isLoaded && isSignedIn && (
            <>
              <span className={styles.emailTextStyle}>{user?.primaryEmailAddress?.emailAddress || 'User'}</span>
              <UserButton afterSignOutUrl="/login" />
            </>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;