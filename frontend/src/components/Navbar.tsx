import { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import * as styles from './navbarStyles.css.ts';

const Navbar = () => {
  const { user, signOut } = useAuthStore();
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const navigate = useNavigate(); // Hook for navigation
  const profileMenuRef = useRef<HTMLDivElement>(null);

  // Close the profile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (profileMenuRef.current && !profileMenuRef.current.contains(event.target as Node)) {
        setIsProfileOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const toggleProfile = () => {
    setIsProfileOpen(!isProfileOpen);
  };

  const handleSignOut = async () => {
    try {
      await signOut();
      navigate('/login'); // Redirect to login page after sign-out
      setIsProfileOpen(false); // Close the profile menu
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return (
    <nav className={styles.navbarStyle}>
      <div className={styles.navbarLeftStyle}>
        <div className={styles.logoContainerStyle}>
          <span className={styles.logoTextStyle}>R</span>
        </div>
        <span className={styles.userNameStyle}>Rolewise.ai</span>
      </div>

      <div className={styles.navbarRightStyle}>
        <div className="relative" ref={profileMenuRef}>
          <button
            type="button"
            className={styles.profileButtonStyle}
            onClick={toggleProfile}
          >
            <span className={styles.srOnlyStyle}>Open user menu</span>
            <div className={styles.profileAvatarStyle}>
              {user?.email?.charAt(0).toUpperCase() || 'U'}
            </div>
            <span className={styles.emailTextStyle}>{user?.email || 'User'}</span>
          </button>

          {isProfileOpen && (
            <div className={styles.profileMenuStyle}>
              <button onClick={handleSignOut} className={styles.signOutButtonStyle}>
                Sign out
              </button>
            </div>
          )}
        </div>
      </div>
    </nav>
  );
};

export default Navbar;