<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Rolewise.ai - Intelligent Identity Management</title>
    <meta name="description" content="Advanced identity management platform using machine learning to derive contextual understanding of access privileges." />
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: #f5f5f5;
        color: #333;
      }
      #root {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
      }
      .fallback {
        display: none; /* Hidden by default, will show if J<PERSON> fails */
        padding: 20px;
        margin: 20px;
        background-color: white;
        border: 1px solid #ddd;
        border-radius: 5px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
      }
      /* Show fallback content if root is empty after 2 seconds */
      @media (prefers-reduced-motion: no-preference) {
        .fallback-timer {
          animation: showFallback 1s 2s forwards;
        }
        @keyframes showFallback {
          to { display: block; }
        }
      }

      /* Direct fix for IntegrationSetup component */
      [class*="container"], [class*="content"], [class*="formGroup"], [class*="label"], [class*="input"], [class*="button"], [class*="result"], [class*="error"], [class*="success"] {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative !important;
        z-index: 1 !important;
      }
      [class*="buttonGroup"] {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        position: relative !important;
        z-index: 1 !important;
      }
      [class*="button"] {
        display: inline-block !important;
      }

      /* Fix for text elements */
      h1, h2, h3, h4, h5, h6, p, span, label, input, button, a {
        visibility: visible !important;
        opacity: 1 !important;
        color: inherit !important;
      }
    </style>
  </head>
  <body style="display: block; visibility: visible; opacity: 1;">
    <div id="root" style="display: block; visibility: visible; opacity: 1;"></div>
    <div id="fallback" class="fallback fallback-timer">
      <h1>Rolewise.ai</h1>
      <p>If you're seeing this message, there might be an issue loading the application.</p>
      <p>Please check the browser console for errors (F12 or right-click > Inspect > Console).</p>
      <div style="margin-top: 20px; padding: 10px; background-color: #f8f8f8; border-radius: 5px;">
        <h2>Diagnostic Information</h2>
        <p>Browser: <span id="browser-info"></span></p>
        <p>Time: <span id="time-info"></span></p>
      </div>
    </div>
    <script>
      // Basic diagnostic info
      document.getElementById('browser-info').textContent = navigator.userAgent;
      document.getElementById('time-info').textContent = new Date().toLocaleString();

      // Check if the root element gets populated
      setTimeout(() => {
        const root = document.getElementById('root');
        const fallback = document.getElementById('fallback');
        if (root && root.children.length === 0) {
          fallback.style.display = 'block';
        }
      }, 2000);
    </script>
    <script type="module" src="/src/main.tsx"></script>
    <script>
      // Fix for vanilla-extract CSS visibility issues
      document.addEventListener('DOMContentLoaded', function() {
        // Force all elements to be visible
        const style = document.createElement('style');
        style.textContent = `
          * {
            visibility: visible !important;
            opacity: 1 !important;
          }
          [class*="container"], [class*="content"], [class*="formGroup"], [class*="label"], [class*="input"], [class*="button"], [class*="result"], [class*="error"], [class*="success"] {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
          }
          [class*="buttonGroup"] {
            display: flex !important;
            visibility: visible !important;
            opacity: 1 !important;
          }
        `;
        document.head.appendChild(style);
      });
    </script>
  </body>
</html>