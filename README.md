# Rolewise.ai - Intelligent Identity Management Platform

Rolewise.ai is an advanced identity management platform that uses machine learning to derive contextual understanding of access privileges. Unlike traditional identity management solutions that require manual configuration of roles and permissions, Rolewise.ai automatically analyzes access patterns and organizational structures to provide intelligent recommendations and insights.

## Key Features

- **Automated Role Discovery**: Automatically identifies and suggests role definitions based on access patterns using machine learning clustering algorithms.
- **Anomaly Detection**: Identifies unusual access patterns that may indicate security risks.
- **Access Insights**: Provides actionable insights to optimize your access management strategy with visual role graphs.
- **User Management**: Comprehensive user management with role-based access control.
- **Permission Management**: Fine-grained permission control with resource and action-based definitions.
- **Multi-Tenant Architecture**: Secure isolation between different organizations with automatic tenant creation.
- **Microsoft Entra ID Integration**: Connect to Microsoft Entra ID (formerly Azure AD) to import users, groups, and applications.

## Technology Stack

- **Frontend**: React 19, TypeScript, React Router 7
- **State Management**: Zustand for global state management
- **Styling**: vanilla-extract CSS for type-safe styling
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Serverless Functions**: Supabase Edge Functions for event-driven processes
- **Testing**: Vitest for unit and integration testing

## Project Structure

```
rolewise.ai/
├── frontend/
│   ├── src/
│   │   ├── components/       # Reusable UI components
│   │   ├── hooks/            # Custom React hooks
│   │   ├── pages/            # Page components
│   │   ├── services/         # API and external service integrations
│   │   │   ├── api/          # API client functions
│   │   │   ├── config.ts     # Environment configuration
│   │   │   └── supabaseClient.ts  # Supabase client
│   │   ├── stores/           # Zustand state stores
│   │   ├── styles/           # Global styles
│   │   ├── types/            # TypeScript type definitions
│   │   └── utils/            # Utility functions
│   ├── public/               # Static assets
│   └── ...
├── supabase/
│   ├── functions/            # Supabase Edge Functions
│   │   ├── fetch-entra-data/       # Fetches data from Microsoft Entra ID
│   │   ├── infer-roles/            # ML-based role inference
│   │   ├── orchestrate-tenant-processing/  # Orchestrates the data processing workflow
│   │   ├── process-tenant-data/    # Processes tenant data
│   │   ├── process-webhooks/       # Processes webhook deliveries
│   │   └── ...
│   ├── migrations/           # Database migrations
│   └── ...
└── ...
```

## Multi-Tenant Architecture

Rolewise.ai uses a multi-tenant architecture where each organization has its own isolated environment:

1. **Tenant Creation**: When a user registers, a new tenant is automatically created.
2. **User Association**: The registering user becomes the admin of the new tenant.
3. **Data Isolation**: Row-level security ensures data is only accessible within the same tenant.
4. **Role-Based Access**: Within each tenant, users can have different roles (admin, user, viewer, etc.).

## Authentication Flow

1. **Registration**: User signs up with email, password, and name.
2. **Tenant Creation**: A Supabase Edge Function automatically creates a new tenant and associates the user with it.
3. **Email Verification**: User verifies their email address (if required).
4. **Login**: User logs in with their credentials.
5. **Session Management**: JWT tokens are used for maintaining authenticated sessions.

## Role Inference Process

Rolewise.ai uses machine learning to automatically discover roles:

1. **Data Collection**: Fetch user, group, and application data from connected systems (e.g., Microsoft Entra ID).
2. **Embedding Generation**: Generate vector embeddings for groups and applications using natural language processing.
3. **Clustering**: Apply k-means clustering to identify patterns in user access.
4. **Role Creation**: Create role definitions based on clusters with confidence scores.
5. **Application Mapping**: Map roles to applications based on semantic similarity.
6. **Job Function Mapping**: Associate roles with standard job functions for better organization.

## Getting Started

### Prerequisites

- Node.js (v18+)
- Supabase account
- Supabase CLI (for local development)
- OpenAI API key (for embedding generation)

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/rolewise.ai.git
   cd rolewise.ai
   ```

2. Set up the frontend:
   ```
   cd frontend
   npm install
   ```

3. Create a `.env` file in the frontend directory:
   ```
   VITE_SUPABASE_URL=your_supabase_url
   VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
   ```

4. Set up Supabase Edge Functions:
   ```
   cd supabase
   supabase functions deploy
   ```

5. Set environment variables for Supabase Edge Functions:
   ```
   supabase secrets set OPENAI_API_KEY=your_openai_api_key
   ```

### Running the Application

1. Start the frontend development server:
   ```
   cd frontend
   npm run dev
   ```

2. Open your browser and navigate to `http://localhost:5173`

## Testing

Run the test suite with:

```
cd frontend
npm test
```

For coverage reports:

```
npm test -- --coverage
```

## License

[MIT License](LICENSE)

## Contact

For questions or support, please contact [<EMAIL>](mailto:<EMAIL>).