import { createClient } from 'jsr:@supabase/supabase-js@2.49.4';
import { corsHeaders } from '../_shared/cors.ts';
import * as crypto from 'https://deno.land/std@0.177.0/crypto/mod.ts';

interface WebhookDelivery {
  id: string;
  webhook_id: string;
  event_type: string;
  payload: Record<string, any>;
  status: 'pending' | 'success' | 'failed';
  status_code: number | null;
  response_body: string | null;
  error: string | null;
  attempt_count: number;
  next_retry_at: string | null;
  created_at: string;
  updated_at: string;
}

interface Webhook {
  id: string;
  tenant_id: string;
  name: string;
  url: string;
  secret: string;
  events: string[];
  is_active: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
}

// Configuration
const MAX_RETRIES = 5;
const RETRY_DELAYS = [
  1 * 60, // 1 minute
  5 * 60, // 5 minutes
  15 * 60, // 15 minutes
  60 * 60, // 1 hour
  6 * 60 * 60 // 6 hours
];
const MAX_CONCURRENT_DELIVERIES = 20;

/**
 * Generate HMAC signature for webhook payload
 */
async function generateSignature(payload: string, secret: string): Promise<string> {
  const encoder = new TextEncoder();
  const key = await crypto.subtle.importKey(
    'raw',
    encoder.encode(secret),
    { name: 'HMAC', hash: 'SHA-256' },
    false,
    ['sign']
  );
  
  const signature = await crypto.subtle.sign(
    'HMAC',
    key,
    encoder.encode(payload)
  );
  
  return Array.from(new Uint8Array(signature))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

/**
 * Process a single webhook delivery
 */
async function processWebhookDelivery(
  delivery: WebhookDelivery,
  webhook: Webhook,
  supabase: ReturnType<typeof createClient>
): Promise<void> {
  console.log(`Processing webhook delivery ${delivery.id} for webhook ${webhook.name} (${webhook.id})`);
  
  try {
    // Prepare the payload
    const payloadString = JSON.stringify(delivery.payload);
    
    // Generate signature
    const timestamp = new Date().getTime().toString();
    const signature = await generateSignature(`${timestamp}.${payloadString}`, webhook.secret);
    
    // Send the webhook
    const response = await fetch(webhook.url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Rolewise-Signature': signature,
        'X-Rolewise-Timestamp': timestamp,
        'X-Rolewise-Event': delivery.event_type,
        'X-Rolewise-Delivery': delivery.id,
      },
      body: payloadString,
    });
    
    // Get response body as text
    let responseBody: string | null = null;
    try {
      responseBody = await response.text();
    } catch (error) {
      console.warn(`Failed to read response body: ${error instanceof Error ? error.message : String(error)}`);
    }
    
    if (response.ok) {
      // Update delivery status to success
      await supabase
        .from('webhook_deliveries')
        .update({
          status: 'success',
          status_code: response.status,
          response_body: responseBody,
          attempt_count: delivery.attempt_count + 1,
          next_retry_at: null,
        })
        .eq('id', delivery.id);
        
      console.log(`Successfully delivered webhook ${delivery.id} to ${webhook.url}`);
    } else {
      // Handle failed delivery
      const errorMessage = `Webhook delivery failed with status ${response.status}: ${responseBody || 'No response body'}`;
      console.error(errorMessage);
      
      // Determine if we should retry
      const shouldRetry = delivery.attempt_count < MAX_RETRIES && 
                          response.status >= 500; // Only retry server errors
      
      if (shouldRetry) {
        const retryIndex = Math.min(delivery.attempt_count, RETRY_DELAYS.length - 1);
        const retryDelay = RETRY_DELAYS[retryIndex];
        const nextRetryAt = new Date(Date.now() + retryDelay * 1000).toISOString();
        
        await supabase
          .from('webhook_deliveries')
          .update({
            status: 'pending',
            status_code: response.status,
            response_body: responseBody,
            error: errorMessage,
            attempt_count: delivery.attempt_count + 1,
            next_retry_at: nextRetryAt,
          })
          .eq('id', delivery.id);
          
        console.log(`Scheduled retry for webhook delivery ${delivery.id} at ${nextRetryAt}`);
      } else {
        // Mark as permanently failed
        await supabase
          .from('webhook_deliveries')
          .update({
            status: 'failed',
            status_code: response.status,
            response_body: responseBody,
            error: errorMessage,
            attempt_count: delivery.attempt_count + 1,
            next_retry_at: null,
          })
          .eq('id', delivery.id);
          
        console.log(`Marked webhook delivery ${delivery.id} as permanently failed after ${delivery.attempt_count + 1} attempts`);
      }
    }
  } catch (error) {
    // Handle exceptions during delivery
    const errorMessage = `Exception during webhook delivery: ${error instanceof Error ? error.message : String(error)}`;
    console.error(errorMessage);
    
    // Determine if we should retry
    const shouldRetry = delivery.attempt_count < MAX_RETRIES;
    
    if (shouldRetry) {
      const retryIndex = Math.min(delivery.attempt_count, RETRY_DELAYS.length - 1);
      const retryDelay = RETRY_DELAYS[retryIndex];
      const nextRetryAt = new Date(Date.now() + retryDelay * 1000).toISOString();
      
      await supabase
        .from('webhook_deliveries')
        .update({
          status: 'pending',
          error: errorMessage,
          attempt_count: delivery.attempt_count + 1,
          next_retry_at: nextRetryAt,
        })
        .eq('id', delivery.id);
        
      console.log(`Scheduled retry for webhook delivery ${delivery.id} at ${nextRetryAt}`);
    } else {
      // Mark as permanently failed
      await supabase
        .from('webhook_deliveries')
        .update({
          status: 'failed',
          error: errorMessage,
          attempt_count: delivery.attempt_count + 1,
          next_retry_at: null,
        })
        .eq('id', delivery.id);
        
      console.log(`Marked webhook delivery ${delivery.id} as permanently failed after ${delivery.attempt_count + 1} attempts`);
    }
  }
}

/**
 * Process pending webhook deliveries
 */
async function processPendingWebhooks(supabase: ReturnType<typeof createClient>): Promise<number> {
  console.log('Processing pending webhook deliveries...');
  
  // Get pending webhook deliveries
  const { data: deliveries, error: deliveriesError } = await supabase
    .from('webhook_deliveries')
    .select('*')
    .eq('status', 'pending')
    .lte('next_retry_at', new Date().toISOString())
    .order('created_at', { ascending: true })
    .limit(MAX_CONCURRENT_DELIVERIES);
    
  if (deliveriesError) {
    console.error(`Error fetching pending webhook deliveries: ${deliveriesError.message}`);
    return 0;
  }
  
  if (!deliveries || deliveries.length === 0) {
    console.log('No pending webhook deliveries found');
    return 0;
  }
  
  console.log(`Found ${deliveries.length} pending webhook deliveries`);
  
  // Get the webhooks for these deliveries
  const webhookIds = [...new Set(deliveries.map(d => d.webhook_id))];
  const { data: webhooks, error: webhooksError } = await supabase
    .from('webhooks')
    .select('*')
    .in('id', webhookIds)
    .eq('is_active', true);
    
  if (webhooksError) {
    console.error(`Error fetching webhooks: ${webhooksError.message}`);
    return 0;
  }
  
  if (!webhooks || webhooks.length === 0) {
    console.log('No active webhooks found for pending deliveries');
    return 0;
  }
  
  // Create a map of webhook IDs to webhooks for quick lookup
  const webhookMap = new Map<string, Webhook>();
  for (const webhook of webhooks) {
    webhookMap.set(webhook.id, webhook);
  }
  
  // Process each delivery
  const promises = deliveries.map(async (delivery) => {
    const webhook = webhookMap.get(delivery.webhook_id);
    if (!webhook) {
      console.log(`Webhook ${delivery.webhook_id} not found or inactive for delivery ${delivery.id}`);
      
      // Mark as failed
      await supabase
        .from('webhook_deliveries')
        .update({
          status: 'failed',
          error: 'Webhook not found or inactive',
          next_retry_at: null,
        })
        .eq('id', delivery.id);
        
      return;
    }
    
    await processWebhookDelivery(delivery, webhook, supabase);
  });
  
  await Promise.all(promises);
  
  return deliveries.length;
}

/**
 * Main handler for the process-webhooks function
 */
Deno.serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS request');
    return new Response(null, {
      status: 204,
      headers: corsHeaders,
    });
  }
  
  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    
    if (!supabaseUrl || !serviceRoleKey) {
      console.error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY');
      return new Response(
        JSON.stringify({ error: 'Server configuration error' }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }
    
    // Create Supabase client with service role key
    const supabase = createClient(supabaseUrl, serviceRoleKey);
    
    // Process pending webhooks
    const processedCount = await processPendingWebhooks(supabase);
    
    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        message: `Processed ${processedCount} webhook deliveries`,
        processedCount,
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );
  } catch (error: unknown) {
    console.error('Error in process-webhooks:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    return new Response(
      JSON.stringify({ error: errorMessage || 'Internal server error' }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );
  }
});
