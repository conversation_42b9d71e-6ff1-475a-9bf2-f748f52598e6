import { createClient } from 'jsr:@supabase/supabase-js@2.49.4';
import { corsHeaders } from '../_shared/cors.ts';

interface OrchestrationPayload {
  tenantId: string;
  runInference?: boolean;
  forceRefresh?: boolean;
}

interface ProcessingJob {
  id: string;
  tenant_id: string;
  status: 'pending' | 'fetching' | 'processing' | 'inferring' | 'completed' | 'failed';
  started_at: string;
  completed_at: string | null;
  error: string | null;
  metadata: Record<string, any>;
}

Deno.serve(async (req) => {
  console.log(`Request: ${req.method} ${req.url}`, {
    headers: Object.fromEntries(req.headers),
  });

  // Handle CORS
  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS request');
    return new Response(null, {
      status: 204,
      headers: corsHeaders,
    });
  }

  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    if (!supabaseUrl || !serviceRoleKey) {
      console.error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY');
      return new Response(
        JSON.stringify({ error: 'Server configuration error' }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    // Validate authentication
    const authHeader = req.headers.get('Authorization');
    console.log('Authorization header:', authHeader ? 'Present' : 'Missing');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing Authorization header' }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    // Create Supabase client with the user's JWT
    const supabaseClient = createClient(supabaseUrl, serviceRoleKey, {
      global: {
        headers: { Authorization: authHeader },
      },
    });

    // Verify the user
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser();
    console.log('auth.getUser result:', {
      userId: user?.id,
      authError: authError?.message,
    });
    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Authentication failed', details: authError?.message }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    // Parse request payload
    const { tenantId, runInference = true, forceRefresh = false }: OrchestrationPayload = await req.json();
    if (!tenantId) {
      return new Response(
        JSON.stringify({ error: 'Missing tenantId' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    // Create admin client for database operations
    const adminClient = createClient(supabaseUrl, serviceRoleKey);

    // Verify user has access to this tenant
    const { data: userTenants, error: tenantError } = await adminClient
      .from('user_tenants')
      .select('*')
      .eq('user_id', user.id)
      .eq('tenant_id', tenantId)
      .maybeSingle();

    console.log('user_tenants query:', {
      user_id: user.id,
      tenant_id: tenantId,
      userTenants,
      tenantError: tenantError ? tenantError.message : null,
    });

    if (tenantError) {
      console.log('Tenant query error:', tenantError);
      if (tenantError.code === '42P01') {
        return new Response(
          JSON.stringify({ error: 'User tenants table does not exist. Please contact your administrator.' }),
          {
            status: 500,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          }
        );
      }
      return new Response(
        JSON.stringify({ error: 'Failed to verify tenant access', details: tenantError.message }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    if (!userTenants) {
      console.log('No tenant access found for user:', { user_id: user.id, tenant_id: tenantId });
      return new Response(
        JSON.stringify({ error: 'Not authorized to access this tenant' }),
        {
          status: 403,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    // Check for existing in-progress job
    if (!forceRefresh) {
      const { data: existingJob, error: jobError } = await adminClient
        .from('processing_jobs')
        .select('*')
        .eq('tenant_id', tenantId)
        .in('status', ['pending', 'fetching', 'processing', 'inferring'])
        .order('started_at', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (jobError) {
        console.error('Error checking for existing jobs:', jobError);
      } else if (existingJob) {
        console.log('Found existing in-progress job:', existingJob);
        return new Response(
          JSON.stringify({
            message: 'Processing already in progress',
            jobId: existingJob.id,
            status: existingJob.status,
            startedAt: existingJob.started_at,
          }),
          {
            status: 200,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          }
        );
      }
    }

    // Create a new processing job
    const { data: newJob, error: createJobError } = await adminClient
      .from('processing_jobs')
      .insert({
        tenant_id: tenantId,
        status: 'pending',
        started_at: new Date().toISOString(),
        completed_at: null,
        error: null,
        metadata: {
          initiated_by: user.id,
          run_inference: runInference,
        },
      })
      .select()
      .single();

    if (createJobError) {
      console.error('Error creating processing job:', createJobError);

      // If the table doesn't exist, create it
      if (createJobError.code === '42P01') {
        console.log('Creating processing_jobs table...');
        await adminClient.rpc('create_processing_jobs_table');

        // Try creating the job again
        const { data: retryJob, error: retryError } = await adminClient
          .from('processing_jobs')
          .insert({
            tenant_id: tenantId,
            status: 'pending',
            started_at: new Date().toISOString(),
            completed_at: null,
            error: null,
            metadata: {
              initiated_by: user.id,
              run_inference: runInference,
            },
          })
          .select()
          .single();

        if (retryError) {
          throw new Error(`Failed to create processing job: ${retryError.message}`);
        }

        console.log('Successfully created processing job on retry:', retryJob);
      } else {
        throw new Error(`Failed to create processing job: ${createJobError.message}`);
      }
    }

    const jobId = newJob?.id;
    console.log('Created new processing job:', { jobId, tenantId });

    // Update job status to fetching
    await adminClient
      .from('processing_jobs')
      .update({ status: 'fetching' })
      .eq('id', jobId);

    // Step 1: Fetch Entra ID data
    console.log('Fetching Entra ID data with forceRefresh:', forceRefresh);
    const fetchResponse = await fetch(`${supabaseUrl}/functions/v1/fetch-entra-data`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader,
      },
      body: JSON.stringify({ tenantId, forceRefresh }),
    });

    if (!fetchResponse.ok) {
      const errorText = await fetchResponse.text();
      console.error('Error fetching Entra ID data:', errorText);

      // Update job status to failed
      await adminClient
        .from('processing_jobs')
        .update({
          status: 'failed',
          completed_at: new Date().toISOString(),
          error: `Failed to fetch Entra ID data: ${errorText}`,
        })
        .eq('id', jobId);

      return new Response(
        JSON.stringify({
          error: 'Failed to fetch Entra ID data',
          details: errorText,
          jobId,
        }),
        {
          status: fetchResponse.status,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    const fetchData = await fetchResponse.json();
    console.log('Successfully fetched Entra ID data:', {
      applications: fetchData.applications?.length || 0,
      groups: fetchData.groups?.length || 0,
      users: fetchData.users?.length || 0,
    });

    // Update job status to processing
    await adminClient
      .from('processing_jobs')
      .update({
        status: 'processing',
        metadata: {
          ...newJob.metadata,
          fetch_completed_at: new Date().toISOString(),
          application_count: fetchData.applications?.length || 0,
          group_count: fetchData.groups?.length || 0,
          user_count: fetchData.users?.length || 0,
        },
      })
      .eq('id', jobId);

    // Step 2: Process tenant data
    console.log('Processing tenant data...');
    const processResponse = await fetch(`${supabaseUrl}/functions/v1/process-tenant-data`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': authHeader,
      },
      body: JSON.stringify({
        tenantId,
        applications: fetchData.applications,
        groups: fetchData.groups,
        users: fetchData.users,
        jobId, // Pass the job ID for status tracking
        runInference, // Let the process function handle inference
        options: {
          batchSize: 100, // Default batch size
          cleanupOldData: false, // Don't clean up by default
          generateEmbeddings: true, // Generate embeddings for applications and groups
        },
      }),
    });

    if (!processResponse.ok) {
      const errorText = await processResponse.text();
      console.error('Error processing tenant data:', errorText);

      // Update job status to failed
      await adminClient
        .from('processing_jobs')
        .update({
          status: 'failed',
          completed_at: new Date().toISOString(),
          error: `Failed to process tenant data: ${errorText}`,
        })
        .eq('id', jobId);

      return new Response(
        JSON.stringify({
          error: 'Failed to process tenant data',
          details: errorText,
          jobId,
        }),
        {
          status: processResponse.status,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    const processData = await processResponse.json();
    console.log('Successfully processed tenant data:', processData);

    // Update job metadata with processing results
    await adminClient
      .from('processing_jobs')
      .update({
        metadata: {
          ...newJob.metadata,
          ...processData,
          process_completed_at: new Date().toISOString(),
        },
      })
      .eq('id', jobId);

    // Note: Role inference is now handled by the process-tenant-data function
    // We don't need to call infer-roles directly anymore

    // Mark job as completed
    await adminClient
      .from('processing_jobs')
      .update({
        status: 'completed',
        completed_at: new Date().toISOString(),
      })
      .eq('id', jobId);

    // Return success response
    return new Response(
      JSON.stringify({
        success: true,
        message: 'Tenant data processing completed successfully',
        jobId,
        runInference,
        applicationCount: fetchData.applications?.length || 0,
        groupCount: fetchData.groups?.length || 0,
        userCount: fetchData.users?.length || 0,
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );
  } catch (error: unknown) {
    console.error('Error in orchestrate-tenant-processing:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    return new Response(
      JSON.stringify({ error: errorMessage || 'Internal server error' }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );
  }
});
