import { createClient } from 'jsr:@supabase/supabase-js@2.49.4';
import { corsHeaders } from '../_shared/cors.ts';
import * as crypto from 'https://deno.land/std@0.177.0/crypto/mod.ts';

interface Webhook {
  id?: string;
  tenant_id: string;
  name: string;
  url: string;
  secret?: string;
  events: string[];
  is_active?: boolean;
}

interface WebhookDelivery {
  id: string;
  webhook_id: string;
  event_type: string;
  payload: Record<string, any>;
  status: 'pending' | 'success' | 'failed';
  status_code: number | null;
  response_body: string | null;
  error: string | null;
  attempt_count: number;
  next_retry_at: string | null;
  created_at: string;
  updated_at: string;
}

// Valid webhook event types
const VALID_EVENT_TYPES = [
  'job.completed',
  'job.failed',
  'tenant.created',
  'tenant.updated',
  'user.created',
  'user.updated'
];

/**
 * Generate a random webhook secret
 */
function generateWebhookSecret(): string {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array)
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

/**
 * Validate webhook URL
 */
function validateWebhookUrl(url: string): boolean {
  try {
    const parsedUrl = new URL(url);
    return parsedUrl.protocol === 'https:';
  } catch (error) {
    return false;
  }
}

/**
 * Validate webhook events
 */
function validateWebhookEvents(events: string[]): boolean {
  if (!Array.isArray(events) || events.length === 0) {
    return false;
  }
  
  return events.every(event => VALID_EVENT_TYPES.includes(event));
}

/**
 * Create a new webhook
 */
async function createWebhook(
  supabase: ReturnType<typeof createClient>,
  userId: string,
  webhook: Webhook
): Promise<Record<string, any>> {
  console.log(`Creating webhook for tenant ${webhook.tenant_id}`);
  
  // Validate webhook URL
  if (!validateWebhookUrl(webhook.url)) {
    throw new Error('Invalid webhook URL. Must be a valid HTTPS URL.');
  }
  
  // Validate webhook events
  if (!validateWebhookEvents(webhook.events)) {
    throw new Error(`Invalid webhook events. Must be one or more of: ${VALID_EVENT_TYPES.join(', ')}`);
  }
  
  // Generate a secret if not provided
  const secret = webhook.secret || generateWebhookSecret();
  
  // Create the webhook
  const { data, error } = await supabase
    .from('webhooks')
    .insert({
      tenant_id: webhook.tenant_id,
      name: webhook.name,
      url: webhook.url,
      secret,
      events: webhook.events,
      is_active: webhook.is_active !== false, // Default to true
      created_by: userId,
    })
    .select()
    .single();
    
  if (error) {
    console.error(`Error creating webhook: ${error.message}`);
    throw new Error(`Failed to create webhook: ${error.message}`);
  }
  
  console.log(`Created webhook ${data.id} for tenant ${webhook.tenant_id}`);
  
  // Return the webhook with the secret
  return {
    ...data,
    secret,
  };
}

/**
 * Update an existing webhook
 */
async function updateWebhook(
  supabase: ReturnType<typeof createClient>,
  userId: string,
  webhookId: string,
  webhook: Partial<Webhook>
): Promise<Record<string, any>> {
  console.log(`Updating webhook ${webhookId}`);
  
  // Validate webhook URL if provided
  if (webhook.url && !validateWebhookUrl(webhook.url)) {
    throw new Error('Invalid webhook URL. Must be a valid HTTPS URL.');
  }
  
  // Validate webhook events if provided
  if (webhook.events && !validateWebhookEvents(webhook.events)) {
    throw new Error(`Invalid webhook events. Must be one or more of: ${VALID_EVENT_TYPES.join(', ')}`);
  }
  
  // Get the existing webhook to check tenant access
  const { data: existingWebhook, error: fetchError } = await supabase
    .from('webhooks')
    .select('tenant_id')
    .eq('id', webhookId)
    .single();
    
  if (fetchError) {
    console.error(`Error fetching webhook: ${fetchError.message}`);
    throw new Error(`Failed to fetch webhook: ${fetchError.message}`);
  }
  
  // Verify user has access to this tenant
  const { data: userTenants, error: tenantError } = await supabase
    .from('user_tenants')
    .select('*')
    .eq('user_id', userId)
    .eq('tenant_id', existingWebhook.tenant_id)
    .maybeSingle();
    
  if (tenantError || !userTenants) {
    console.error(`User ${userId} not authorized for tenant ${existingWebhook.tenant_id}`);
    throw new Error('Not authorized to update this webhook');
  }
  
  // Prepare update data
  const updateData: Record<string, any> = {};
  
  if (webhook.name !== undefined) updateData.name = webhook.name;
  if (webhook.url !== undefined) updateData.url = webhook.url;
  if (webhook.events !== undefined) updateData.events = webhook.events;
  if (webhook.is_active !== undefined) updateData.is_active = webhook.is_active;
  
  // Generate a new secret if requested
  let newSecret: string | undefined;
  if (webhook.secret === 'generate_new') {
    newSecret = generateWebhookSecret();
    updateData.secret = newSecret;
  }
  
  // Update the webhook
  const { data, error } = await supabase
    .from('webhooks')
    .update(updateData)
    .eq('id', webhookId)
    .select()
    .single();
    
  if (error) {
    console.error(`Error updating webhook: ${error.message}`);
    throw new Error(`Failed to update webhook: ${error.message}`);
  }
  
  console.log(`Updated webhook ${webhookId}`);
  
  // Return the webhook with the new secret if generated
  return newSecret ? { ...data, secret: newSecret } : data;
}

/**
 * Delete a webhook
 */
async function deleteWebhook(
  supabase: ReturnType<typeof createClient>,
  userId: string,
  webhookId: string
): Promise<void> {
  console.log(`Deleting webhook ${webhookId}`);
  
  // Get the existing webhook to check tenant access
  const { data: existingWebhook, error: fetchError } = await supabase
    .from('webhooks')
    .select('tenant_id')
    .eq('id', webhookId)
    .single();
    
  if (fetchError) {
    console.error(`Error fetching webhook: ${fetchError.message}`);
    throw new Error(`Failed to fetch webhook: ${fetchError.message}`);
  }
  
  // Verify user has access to this tenant
  const { data: userTenants, error: tenantError } = await supabase
    .from('user_tenants')
    .select('*')
    .eq('user_id', userId)
    .eq('tenant_id', existingWebhook.tenant_id)
    .maybeSingle();
    
  if (tenantError || !userTenants) {
    console.error(`User ${userId} not authorized for tenant ${existingWebhook.tenant_id}`);
    throw new Error('Not authorized to delete this webhook');
  }
  
  // Delete the webhook
  const { error } = await supabase
    .from('webhooks')
    .delete()
    .eq('id', webhookId);
    
  if (error) {
    console.error(`Error deleting webhook: ${error.message}`);
    throw new Error(`Failed to delete webhook: ${error.message}`);
  }
  
  console.log(`Deleted webhook ${webhookId}`);
}

/**
 * List webhooks for a tenant
 */
async function listWebhooks(
  supabase: ReturnType<typeof createClient>,
  userId: string,
  tenantId: string
): Promise<Record<string, any>[]> {
  console.log(`Listing webhooks for tenant ${tenantId}`);
  
  // Verify user has access to this tenant
  const { data: userTenants, error: tenantError } = await supabase
    .from('user_tenants')
    .select('*')
    .eq('user_id', userId)
    .eq('tenant_id', tenantId)
    .maybeSingle();
    
  if (tenantError || !userTenants) {
    console.error(`User ${userId} not authorized for tenant ${tenantId}`);
    throw new Error('Not authorized to access this tenant');
  }
  
  // Get webhooks for this tenant
  const { data, error } = await supabase
    .from('webhooks')
    .select('id, name, url, events, is_active, created_at, updated_at')
    .eq('tenant_id', tenantId)
    .order('created_at', { ascending: false });
    
  if (error) {
    console.error(`Error listing webhooks: ${error.message}`);
    throw new Error(`Failed to list webhooks: ${error.message}`);
  }
  
  return data || [];
}

/**
 * Get webhook deliveries for a webhook
 */
async function getWebhookDeliveries(
  supabase: ReturnType<typeof createClient>,
  userId: string,
  webhookId: string,
  limit: number = 50
): Promise<Record<string, any>[]> {
  console.log(`Getting deliveries for webhook ${webhookId}`);
  
  // Get the webhook to check tenant access
  const { data: webhook, error: webhookError } = await supabase
    .from('webhooks')
    .select('tenant_id')
    .eq('id', webhookId)
    .single();
    
  if (webhookError) {
    console.error(`Error fetching webhook: ${webhookError.message}`);
    throw new Error(`Failed to fetch webhook: ${webhookError.message}`);
  }
  
  // Verify user has access to this tenant
  const { data: userTenants, error: tenantError } = await supabase
    .from('user_tenants')
    .select('*')
    .eq('user_id', userId)
    .eq('tenant_id', webhook.tenant_id)
    .maybeSingle();
    
  if (tenantError || !userTenants) {
    console.error(`User ${userId} not authorized for tenant ${webhook.tenant_id}`);
    throw new Error('Not authorized to access this webhook');
  }
  
  // Get deliveries for this webhook
  const { data, error } = await supabase
    .from('webhook_deliveries')
    .select('id, event_type, status, status_code, error, attempt_count, created_at, updated_at')
    .eq('webhook_id', webhookId)
    .order('created_at', { ascending: false })
    .limit(limit);
    
  if (error) {
    console.error(`Error getting webhook deliveries: ${error.message}`);
    throw new Error(`Failed to get webhook deliveries: ${error.message}`);
  }
  
  return data || [];
}

/**
 * Main handler for the manage-webhooks function
 */
Deno.serve(async (req) => {
  // Handle CORS
  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS request');
    return new Response(null, {
      status: 204,
      headers: corsHeaders,
    });
  }
  
  try {
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');
    
    if (!supabaseUrl || !serviceRoleKey) {
      console.error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY');
      return new Response(
        JSON.stringify({ error: 'Server configuration error' }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }
    
    // Validate authentication
    const authHeader = req.headers.get('Authorization');
    console.log('Authorization header:', authHeader ? 'Present' : 'Missing');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing Authorization header' }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }
    
    // Create Supabase client with service role key
    const supabase = createClient(supabaseUrl, serviceRoleKey);
    
    // Verify the JWT token
    const jwt = authHeader.replace('Bearer ', '');
    const { data: userData, error: verifyError } = await supabase.auth.getUser(jwt);
    if (verifyError || !userData?.user) {
      console.error('Failed to verify user:', verifyError?.message);
      return new Response(
        JSON.stringify({ error: 'Not authorized - Invalid session' }),
        { status: 401, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
      );
    }
    
    const userId = userData.user.id;
    
    // Parse URL to determine the action
    const url = new URL(req.url);
    const pathParts = url.pathname.split('/').filter(Boolean);
    const action = pathParts[pathParts.length - 1];
    
    // Handle different actions based on HTTP method and path
    if (req.method === 'GET') {
      if (action === 'list') {
        // List webhooks for a tenant
        const tenantId = url.searchParams.get('tenantId');
        if (!tenantId) {
          return new Response(
            JSON.stringify({ error: 'Missing tenantId parameter' }),
            { status: 400, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
          );
        }
        
        const webhooks = await listWebhooks(supabase, userId, tenantId);
        return new Response(
          JSON.stringify({ webhooks }),
          { status: 200, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
        );
      } else if (action === 'deliveries') {
        // Get webhook deliveries
        const webhookId = url.searchParams.get('webhookId');
        if (!webhookId) {
          return new Response(
            JSON.stringify({ error: 'Missing webhookId parameter' }),
            { status: 400, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
          );
        }
        
        const limit = parseInt(url.searchParams.get('limit') || '50', 10);
        const deliveries = await getWebhookDeliveries(supabase, userId, webhookId, limit);
        return new Response(
          JSON.stringify({ deliveries }),
          { status: 200, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
        );
      } else if (action === 'events') {
        // List available event types
        return new Response(
          JSON.stringify({ events: VALID_EVENT_TYPES }),
          { status: 200, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
        );
      }
    } else if (req.method === 'POST' && action === 'create') {
      // Create a new webhook
      const payload = await req.json();
      const webhook = await createWebhook(supabase, userId, payload);
      return new Response(
        JSON.stringify({ webhook }),
        { status: 201, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
      );
    } else if (req.method === 'PUT' && action === 'update') {
      // Update an existing webhook
      const payload = await req.json();
      if (!payload.id) {
        return new Response(
          JSON.stringify({ error: 'Missing webhook ID' }),
          { status: 400, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
        );
      }
      
      const webhook = await updateWebhook(supabase, userId, payload.id, payload);
      return new Response(
        JSON.stringify({ webhook }),
        { status: 200, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
      );
    } else if (req.method === 'DELETE') {
      // Delete a webhook
      const webhookId = url.searchParams.get('id');
      if (!webhookId) {
        return new Response(
          JSON.stringify({ error: 'Missing webhook ID' }),
          { status: 400, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
        );
      }
      
      await deleteWebhook(supabase, userId, webhookId);
      return new Response(
        JSON.stringify({ success: true }),
        { status: 200, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
      );
    }
    
    // If we get here, the action is not supported
    return new Response(
      JSON.stringify({ error: 'Unsupported action' }),
      { status: 400, headers: { 'Content-Type': 'application/json', ...corsHeaders } }
    );
  } catch (error: unknown) {
    console.error('Error in manage-webhooks:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    return new Response(
      JSON.stringify({ error: errorMessage || 'Internal server error' }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );
  }
});
