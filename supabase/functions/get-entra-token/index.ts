import { corsHeaders } from '../_shared/cors.ts'
import { CacheManager } from '../_shared/cache.ts';
import { createClient } from 'jsr:@supabase/supabase-js@2.49.4';

interface TokenRequest {
  tenant_id: string;
  client_id: string;
  client_secret: string;
  test_only?: boolean;
  force_refresh?: boolean;
}

console.log("Starting get-entra-token function...");

Deno.serve(async (req) => {
  // Add a health check endpoint
  const url = new URL(req.url);
  if (url.pathname === '/health' || url.pathname === '/') {
    return new Response(
      JSON.stringify({ status: 'ok', message: 'get-entra-token function is running' }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );
  }

  // Handle CORS
  if (req.method === 'OPTIONS') {
    console.log('Handling OPTIONS request');
    return new Response(null, {
      status: 204,
      headers: corsHeaders,
    });
  }

  try {
    // Log request method and headers for debugging
    console.log(`Request method: ${req.method}`);
    console.log('Request headers:', Object.fromEntries(req.headers.entries()));

    // Check if the request has a body before trying to parse it
    const contentType = req.headers.get('content-type') || '';
    console.log(`Content-Type: ${contentType}`);

    if (!contentType.includes('application/json')) {
      console.log('Error: Content-Type is not application/json');
      return new Response(
        JSON.stringify({ error: 'Content-Type must be application/json' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    // Get the request body text and check if it's empty
    const bodyText = await req.text();
    console.log(`Request body received: ${bodyText ? 'Yes' : 'No'}`);
    if (!bodyText) {
      console.log('Error: Empty request body');
      return new Response(
        JSON.stringify({ error: 'Request body is required' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    // Parse the JSON
    let requestData: TokenRequest;
    try {
      requestData = JSON.parse(bodyText) as TokenRequest;
      console.log('Request data parsed successfully');
    } catch (e) {
      console.log(`JSON parse error: ${e.message}`);
      return new Response(
        JSON.stringify({ error: 'Invalid JSON in request body' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    const { tenant_id, client_id, client_secret, test_only, force_refresh } = requestData;
    console.log(`Parameters: tenant_id=${!!tenant_id}, client_id=${!!client_id}, client_secret=${!!client_secret}, force_refresh=${!!force_refresh}`);

    if (!tenant_id || !client_id || !client_secret) {
      console.log('Error: Missing required parameters');
      return new Response(
        JSON.stringify({ error: 'Missing required parameters' }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    // Initialize cache manager
    const supabaseUrl = Deno.env.get('SUPABASE_URL');
    const serviceRoleKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

    if (!supabaseUrl || !serviceRoleKey) {
      console.error('Missing SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY');
      return new Response(
        JSON.stringify({ error: 'Server configuration error' }),
        {
          status: 500,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    const cacheManager = new CacheManager(supabaseUrl, serviceRoleKey);

    // Check cache for existing token if not forcing refresh
    if (!force_refresh) {
      console.log('Checking cache for existing token');
      const cachedToken = await cacheManager.getToken(tenant_id, client_id, client_secret);

      if (cachedToken) {
        console.log('Using cached token');

        if (test_only) {
          return new Response(
            JSON.stringify({ success: true, cached: true }),
            {
              status: 200,
              headers: { 'Content-Type': 'application/json', ...corsHeaders },
            }
          );
        }

        return new Response(
          JSON.stringify({ access_token: cachedToken, cached: true }),
          {
            status: 200,
            headers: { 'Content-Type': 'application/json', ...corsHeaders },
          }
        );
      }

      console.log('No valid cached token found, requesting new token');
    } else {
      console.log('Force refresh requested, skipping cache');
    }

    const tokenEndpoint = `https://login.microsoftonline.com/${tenant_id}/oauth2/v2.0/token`;
    console.log(`Requesting token from: ${tokenEndpoint}`);

    const tokenResponse = await fetch(tokenEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'client_credentials',
        client_id: client_id,
        client_secret: client_secret,
        scope: 'https://graph.microsoft.com/.default',
      }),
    });

    console.log(`Token response status: ${tokenResponse.status}`);
    const tokenData = await tokenResponse.json();
    console.log('Token response data:', tokenData.error || 'Success');

    if (test_only) {
      return new Response(
        JSON.stringify({ success: tokenData.access_token ? true : false, details: tokenData }),
        {
          status: 200,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    if (!tokenData.access_token) {
      return new Response(
        JSON.stringify({ error: 'Failed to obtain access token', details: tokenData }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
    }

    // Cache the token if we got a valid one
    if (tokenData.access_token && tokenData.expires_in) {
      console.log(`Caching token with expiration: ${tokenData.expires_in} seconds`);
      try {
        await cacheManager.saveToken(
          tenant_id,
          client_id,
          tokenData.access_token,
          tokenData.expires_in
        );
      } catch (cacheError) {
        // Log but don't fail if caching fails
        console.error('Error caching token:', cacheError);
      }
    }

    return new Response(
      JSON.stringify({
        access_token: tokenData.access_token,
        cached: false,
        expires_in: tokenData.expires_in
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
        }
      );
  } catch (error) {
    console.error('Error:', error);
    return new Response(
      JSON.stringify({ error: 'Internal server error', message: error.message }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );
  }
});