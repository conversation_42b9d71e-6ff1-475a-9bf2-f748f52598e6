/**
 * Cache utility for Supabase Edge Functions
 * Provides caching functionality for tokens and API responses
 */

import { createClient, SupabaseClient } from 'jsr:@supabase/supabase-js@2';

// Default cache expiration times
const DEFAULT_TOKEN_TTL = 3600 * 1000; // 1 hour in milliseconds
const DEFAULT_DATA_TTL = 24 * 3600 * 1000; // 24 hours in milliseconds

interface CacheOptions {
  ttl?: number;
  forceRefresh?: boolean;
}

/**
 * Cache manager for Supabase Edge Functions
 */
export class CacheManager {
  private supabaseClient: SupabaseClient;
  
  constructor(supabaseUrl: string, serviceRoleKey: string) {
    this.supabaseClient = createClient(supabaseUrl, serviceRoleKey);
  }

  /**
   * Get a cached token for a tenant
   * @param tenantId The tenant ID
   * @param clientId The client ID
   * @param clientSecret The client secret
   * @param options Cache options
   * @returns The cached token or null if not found or expired
   */
  async getToken(
    tenantId: string,
    clientId: string,
    clientSecret: string,
    options: CacheOptions = {}
  ): Promise<string | null> {
    // If force refresh is requested, skip cache lookup
    if (options.forceRefresh) {
      return null;
    }

    try {
      const { data, error } = await this.supabaseClient
        .from('integrations')
        .select('access_token, token_expires_at')
        .eq('app_tenant_id', tenantId)
        .eq('client_id', clientId)
        .maybeSingle();

      if (error || !data || !data.access_token || !data.token_expires_at) {
        return null;
      }

      // Check if token is expired
      const expiresAt = new Date(data.token_expires_at).getTime();
      const now = Date.now();
      
      // Add a 5-minute buffer to avoid edge cases
      if (expiresAt - now < 5 * 60 * 1000) {
        console.log('Token is expired or about to expire');
        return null;
      }

      console.log('Using cached token, expires in', Math.floor((expiresAt - now) / 1000 / 60), 'minutes');
      return data.access_token;
    } catch (error) {
      console.error('Error getting cached token:', error);
      return null;
    }
  }

  /**
   * Save a token to the cache
   * @param tenantId The tenant ID
   * @param clientId The client ID
   * @param accessToken The access token to cache
   * @param expiresIn Token expiration time in seconds
   */
  async saveToken(
    tenantId: string,
    clientId: string,
    accessToken: string,
    expiresIn: number
  ): Promise<void> {
    try {
      // Calculate expiration time
      const expiresAt = new Date(Date.now() + expiresIn * 1000);
      
      // Update the integration record with the token
      const { error } = await this.supabaseClient
        .from('integrations')
        .update({
          access_token: accessToken,
          token_expires_at: expiresAt.toISOString(),
        })
        .eq('app_tenant_id', tenantId)
        .eq('client_id', clientId);

      if (error) {
        console.error('Error saving token to cache:', error);
      } else {
        console.log('Token cached successfully, expires at:', expiresAt.toISOString());
      }
    } catch (error) {
      console.error('Error saving token to cache:', error);
    }
  }

  /**
   * Get cached Microsoft Graph data
   * @param tenantId The tenant ID
   * @param dataType The type of data (e.g., 'applications', 'groups', 'users')
   * @param options Cache options
   * @returns The cached data or null if not found or expired
   */
  async getData(
    tenantId: string,
    dataType: string,
    options: CacheOptions = {}
  ): Promise<any | null> {
    // If force refresh is requested, skip cache lookup
    if (options.forceRefresh) {
      return null;
    }

    try {
      const { data, error } = await this.supabaseClient
        .from('tenant_data_cache')
        .select('data, updated_at')
        .eq('tenant_id', tenantId)
        .eq('data_type', dataType)
        .maybeSingle();

      if (error || !data) {
        return null;
      }

      // Check if data is stale
      const updatedAt = new Date(data.updated_at).getTime();
      const now = Date.now();
      const ttl = options.ttl || DEFAULT_DATA_TTL;
      
      if (now - updatedAt > ttl) {
        console.log(`Cached ${dataType} data is stale`);
        return null;
      }

      console.log(`Using cached ${dataType} data, age:`, Math.floor((now - updatedAt) / 1000 / 60), 'minutes');
      return data.data;
    } catch (error) {
      console.error(`Error getting cached ${dataType} data:`, error);
      return null;
    }
  }

  /**
   * Save data to the cache
   * @param tenantId The tenant ID
   * @param dataType The type of data (e.g., 'applications', 'groups', 'users')
   * @param data The data to cache
   */
  async saveData(
    tenantId: string,
    dataType: string,
    data: any
  ): Promise<void> {
    try {
      // Upsert the data into the cache table
      const { error } = await this.supabaseClient
        .from('tenant_data_cache')
        .upsert({
          tenant_id: tenantId,
          data_type: dataType,
          data,
          updated_at: new Date().toISOString(),
        }, {
          onConflict: 'tenant_id,data_type'
        });

      if (error) {
        console.error(`Error saving ${dataType} data to cache:`, error);
      } else {
        console.log(`${dataType} data cached successfully`);
      }
    } catch (error) {
      console.error(`Error saving ${dataType} data to cache:`, error);
    }
  }

  /**
   * Invalidate all cached data for a tenant
   * @param tenantId The tenant ID
   */
  async invalidateCache(tenantId: string): Promise<void> {
    try {
      // Clear token cache
      const { error: tokenError } = await this.supabaseClient
        .from('integrations')
        .update({
          access_token: null,
          token_expires_at: null,
        })
        .eq('tenant_id', tenantId);

      if (tokenError) {
        console.error('Error invalidating token cache:', tokenError);
      }

      // Clear data cache
      const { error: dataError } = await this.supabaseClient
        .from('tenant_data_cache')
        .delete()
        .eq('tenant_id', tenantId);

      if (dataError) {
        console.error('Error invalidating data cache:', dataError);
      }

      console.log('Cache invalidated for tenant:', tenantId);
    } catch (error) {
      console.error('Error invalidating cache:', error);
    }
  }
}
