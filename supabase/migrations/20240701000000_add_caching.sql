-- Add token caching columns to integrations table
ALTER TABLE public.integrations
ADD COLUMN IF NOT EXISTS access_token TEXT,
ADD COLUMN IF NOT EXISTS token_expires_at TIMESTAMPTZ;

-- Ensure RLS is enabled on integrations table
ALTER TABLE public.integrations ENABLE ROW LEVEL SECURITY;

-- Service role can do anything with integrations
-- First drop the policy if it exists to avoid errors
DROP POLICY IF EXISTS "Service role can do anything with integrations" ON public.integrations;
CREATE POLICY "Service role can do anything with integrations"
  ON public.integrations
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Users can only read non-sensitive integration data for tenants they have access to
DROP POLICY IF EXISTS "Users can read their tenant's integration data" ON public.integrations;
CREATE POLICY "Users can read their tenant's integration data"
  ON public.integrations
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.user_tenants ut
      WHERE ut.user_id = auth.uid()
      AND ut.tenant_id = integrations.tenant_id
    )
  );

-- Create a security view that hides sensitive fields
CREATE OR REPLACE VIEW public.integrations_secure AS
SELECT
  id,
  tenant_id,
  client_id,
  app_tenant_id,
  created_at,
  updated_at,
  -- Exclude client_secret, access_token, and token_expires_at
  CASE WHEN token_expires_at IS NOT NULL THEN true ELSE false END AS has_cached_token
FROM public.integrations;

-- Create a table for caching Microsoft Graph data
CREATE TABLE IF NOT EXISTS public.tenant_data_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID NOT NULL REFERENCES public.tenants(id) ON DELETE CASCADE,
  data_type TEXT NOT NULL,
  data JSONB NOT NULL,
  updated_at TIMESTAMPTZ NOT NULL DEFAULT now(),
  UNIQUE(tenant_id, data_type)
);

-- Add RLS policies for the new table
ALTER TABLE public.tenant_data_cache ENABLE ROW LEVEL SECURITY;

-- Service role can do anything with the cache table
DROP POLICY IF EXISTS "Service role can do anything with tenant_data_cache" ON public.tenant_data_cache;
CREATE POLICY "Service role can do anything with tenant_data_cache"
  ON public.tenant_data_cache
  FOR ALL
  TO service_role
  USING (true)
  WITH CHECK (true);

-- Users can only read cache data for tenants they have access to
DROP POLICY IF EXISTS "Users can read their tenant's cache data" ON public.tenant_data_cache;
CREATE POLICY "Users can read their tenant's cache data"
  ON public.tenant_data_cache
  FOR SELECT
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM public.user_tenants ut
      WHERE ut.user_id = auth.uid()
      AND ut.tenant_id = tenant_data_cache.tenant_id
    )
  );

-- Only allow edge functions (via service role) to insert/update/delete cache data
DROP POLICY IF EXISTS "Only service role can modify cache data" ON public.tenant_data_cache;
CREATE POLICY "Only service role can modify cache data"
  ON public.tenant_data_cache
  FOR INSERT
  TO service_role
  WITH CHECK (true);

DROP POLICY IF EXISTS "Only service role can update cache data" ON public.tenant_data_cache;
CREATE POLICY "Only service role can update cache data"
  ON public.tenant_data_cache
  FOR UPDATE
  TO service_role
  USING (true)
  WITH CHECK (true);

DROP POLICY IF EXISTS "Only service role can delete cache data" ON public.tenant_data_cache;
CREATE POLICY "Only service role can delete cache data"
  ON public.tenant_data_cache
  FOR DELETE
  TO service_role
  USING (true);

-- Add comment to explain the purpose of the table
COMMENT ON TABLE public.tenant_data_cache IS 'Stores cached Microsoft Graph API data for tenants';

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_tenant_data_cache_tenant_data_type
  ON public.tenant_data_cache (tenant_id, data_type);

-- Create a function to invalidate cache when integration settings change
CREATE OR REPLACE FUNCTION public.invalidate_tenant_cache()
RETURNS TRIGGER AS $$
BEGIN
  -- Clear token cache in the integrations table
  UPDATE public.integrations
  SET access_token = NULL, token_expires_at = NULL
  WHERE tenant_id = NEW.tenant_id;

  -- Delete all cached data for this tenant
  DELETE FROM public.tenant_data_cache
  WHERE tenant_id = NEW.tenant_id;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger to invalidate cache when integration settings change
DROP TRIGGER IF EXISTS invalidate_cache_on_integration_update ON public.integrations;
CREATE TRIGGER invalidate_cache_on_integration_update
  AFTER UPDATE OF client_id, client_secret, app_tenant_id
  ON public.integrations
  FOR EACH ROW
  EXECUTE FUNCTION public.invalidate_tenant_cache();

-- Create a function to clean up old cache entries
CREATE OR REPLACE FUNCTION public.cleanup_old_cache_entries()
RETURNS void AS $$
DECLARE
  max_age_days INTEGER := 7; -- Maximum age of cache entries in days
BEGIN
  -- Delete expired tokens
  UPDATE public.integrations
  SET access_token = NULL, token_expires_at = NULL
  WHERE token_expires_at < NOW();

  -- Delete old cache entries
  DELETE FROM public.tenant_data_cache
  WHERE updated_at < NOW() - (max_age_days * INTERVAL '1 day');
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a cron job to clean up old cache entries daily
-- Note: This requires pg_cron extension to be enabled
-- Commented out for now as it requires the pg_cron extension
-- Uncomment this if you have pg_cron available
/*
DO $$
BEGIN
  IF EXISTS (
    SELECT 1 FROM pg_extension WHERE extname = 'pg_cron'
  ) THEN
    -- Schedule the cleanup function to run daily at 3 AM
    PERFORM cron.schedule('0 3 * * *', 'SELECT public.cleanup_old_cache_entries()');
  END IF;
END $$;
*/

-- Instead, we'll add a comment to remind about manual cleanup
COMMENT ON FUNCTION public.cleanup_old_cache_entries() IS
  'Run this function periodically to clean up old cache entries. Example: SELECT public.cleanup_old_cache_entries();';
