-- Migration to enhance role inference storage
-- Adds metadata fields to roles table and creates role_job_function_mappings table

-- Enhance the roles table with additional metadata
ALTER TABLE public.roles 
  ADD COLUMN IF NOT EXISTS source TEXT CHECK (source IN ('manual', 'inferred', 'imported')) DEFAULT 'manual',
  ADD COLUMN IF NOT EXISTS inference_method TEXT,
  ADD COLUMN IF NOT EXISTS inference_version TEXT,
  ADD COLUMN IF NOT EXISTS inference_date TIMESTAMPTZ,
  ADD COLUMN IF NOT EXISTS confidence_score FLOAT,
  ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}'::jsonb,
  ADD COLUMN IF NOT EXISTS created_at TIMESTAMPTZ DEFAULT NOW(),
  ADD COLUMN IF NOT EXISTS updated_at TIMESTAMPTZ DEFAULT NOW();

-- Add comment to explain the new fields
COMMENT ON COLUMN public.roles.source IS 'Source of the role: manual (user-created), inferred (AI-generated), or imported';
COMMENT ON COLUMN public.roles.inference_method IS 'Method used to infer the role (e.g., clustering, embedding similarity)';
COMMENT ON COLUMN public.roles.inference_version IS 'Version of the inference algorithm used';
COMMENT ON COLUMN public.roles.inference_date IS 'Date when the role was inferred';
COMMENT ON COLUMN public.roles.confidence_score IS 'Confidence score for inferred roles (0-1)';
COMMENT ON COLUMN public.roles.metadata IS 'Additional metadata about the role inference process';

-- Create a trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_roles_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_roles_updated_at_trigger
BEFORE UPDATE ON public.roles
FOR EACH ROW
EXECUTE FUNCTION update_roles_updated_at();

-- Create role_job_function_mappings table
CREATE TABLE IF NOT EXISTS public.role_job_function_mappings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  role_id UUID NOT NULL REFERENCES public.roles(id) ON DELETE CASCADE,
  tenant_id UUID NOT NULL REFERENCES public.tenants(id) ON DELETE CASCADE,
  job_function_title TEXT NOT NULL,
  confidence_score FLOAT NOT NULL,
  mapping_method TEXT,
  mapping_version TEXT,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Add comment to explain the purpose of the table
COMMENT ON TABLE public.role_job_function_mappings IS 'Stores mappings between roles and standard job functions with confidence scores';

-- Create index for faster lookups
CREATE INDEX IF NOT EXISTS idx_role_job_function_mappings_role_id
  ON public.role_job_function_mappings (role_id);

CREATE INDEX IF NOT EXISTS idx_role_job_function_mappings_tenant_id
  ON public.role_job_function_mappings (tenant_id);

-- Create a trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_role_job_function_mappings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_role_job_function_mappings_updated_at_trigger
BEFORE UPDATE ON public.role_job_function_mappings
FOR EACH ROW
EXECUTE FUNCTION update_role_job_function_mappings_updated_at();

-- Enable RLS on the new table
ALTER TABLE public.role_job_function_mappings ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for the role_job_function_mappings table
CREATE POLICY "Users can view role job function mappings for their tenants" ON public.role_job_function_mappings
  FOR SELECT
  USING (
    tenant_id IN (
      SELECT tenant_id FROM public.user_tenants
      WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Service role can do anything with role_job_function_mappings" ON public.role_job_function_mappings
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');
