-- Create the processing_jobs table if it doesn't exist
CREATE TABLE IF NOT EXISTS processing_jobs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  status TEXT NOT NULL CHECK (status IN ('pending', 'fetching', 'processing', 'inferring', 'completed', 'failed')),
  started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  completed_at TIMESTAMPTZ,
  error TEXT,
  metadata JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS processing_jobs_tenant_id_idx ON processing_jobs(tenant_id);
CREATE INDEX IF NOT EXISTS processing_jobs_status_idx ON processing_jobs(status);
CREATE INDEX IF NOT EXISTS processing_jobs_started_at_idx ON processing_jobs(started_at);

-- Create a function to create the processing_jobs table if it doesn't exist
CREATE OR REPLACE FUNCTION create_processing_jobs_table()
RETURNS void AS $$
BEGIN
  -- Check if the table exists
  IF NOT EXISTS (
    SELECT FROM pg_tables 
    WHERE schemaname = 'public' 
    AND tablename = 'processing_jobs'
  ) THEN
    -- Create the table
    EXECUTE '
      CREATE TABLE processing_jobs (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        tenant_id UUID NOT NULL,
        status TEXT NOT NULL CHECK (status IN (''pending'', ''fetching'', ''processing'', ''inferring'', ''completed'', ''failed'')),
        started_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        completed_at TIMESTAMPTZ,
        error TEXT,
        metadata JSONB DEFAULT ''{}'',
        created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
        updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
      );
      
      -- Create indexes for better query performance
      CREATE INDEX processing_jobs_tenant_id_idx ON processing_jobs(tenant_id);
      CREATE INDEX processing_jobs_status_idx ON processing_jobs(status);
      CREATE INDEX processing_jobs_started_at_idx ON processing_jobs(started_at);
    ';
  END IF;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_modified_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply the trigger to the processing_jobs table
DROP TRIGGER IF EXISTS set_updated_at ON processing_jobs;
CREATE TRIGGER set_updated_at
BEFORE UPDATE ON processing_jobs
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Create RLS policies for the processing_jobs table
ALTER TABLE processing_jobs ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their tenant's jobs
CREATE POLICY view_tenant_jobs ON processing_jobs
  FOR SELECT
  USING (
    tenant_id IN (
      SELECT tenant_id FROM user_tenants
      WHERE user_id = auth.uid()
    )
  );

-- Policy for users to insert jobs for their tenant
CREATE POLICY insert_tenant_jobs ON processing_jobs
  FOR INSERT
  WITH CHECK (
    tenant_id IN (
      SELECT tenant_id FROM user_tenants
      WHERE user_id = auth.uid()
    )
  );

-- Policy for users to update jobs for their tenant
CREATE POLICY update_tenant_jobs ON processing_jobs
  FOR UPDATE
  USING (
    tenant_id IN (
      SELECT tenant_id FROM user_tenants
      WHERE user_id = auth.uid()
    )
  );
