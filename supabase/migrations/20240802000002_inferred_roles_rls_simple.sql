-- Simple migration to enable <PERSON><PERSON> on inferred_roles table with basic policies

-- Enable RLS on the inferred_roles table
ALTER TABLE public.inferred_roles ENABLE ROW LEVEL SECURITY;

-- Add comment to explain the purpose of the table
COMMENT ON TABLE public.inferred_roles IS 'Stores inferred role assignments for users with confidence scores and metadata';

-- <PERSON>reate indexes for better performance
CREATE INDEX IF NOT EXISTS idx_inferred_roles_tenant_id
  ON public.inferred_roles (tenant_id);

CREATE INDEX IF NOT EXISTS idx_inferred_roles_user_id
  ON public.inferred_roles (user_id);

-- Create a simple policy for service role
CREATE POLICY "Service role can do anything with inferred_roles" ON public.inferred_roles
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- Create a simple policy for users to view their own inferred roles
CREATE POLICY "Users can view their own inferred roles" ON public.inferred_roles
  FOR SELECT
  USING (
    user_id::text = auth.uid()::text
  );
