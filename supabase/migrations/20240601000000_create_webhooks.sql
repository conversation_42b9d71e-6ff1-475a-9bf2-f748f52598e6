-- Create the webhooks table
CREATE TABLE IF NOT EXISTS webhooks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  url TEXT NOT NULL,
  secret TEXT NOT NULL,
  events TEXT[] NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT true,
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS webhooks_tenant_id_idx ON webhooks(tenant_id);
CREATE INDEX IF NOT EXISTS webhooks_events_idx ON webhooks USING GIN(events);

-- Create a trigger to update the updated_at timestamp
DROP TRIGGER IF EXISTS set_webhooks_updated_at ON webhooks;
CREATE TRIGGER set_webhooks_updated_at
BEFORE UPDATE ON webhooks
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Create the webhook_deliveries table to track webhook delivery attempts
CREATE TABLE IF NOT EXISTS webhook_deliveries (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  webhook_id UUID NOT NULL REFERENCES webhooks(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL,
  payload JSONB NOT NULL,
  status TEXT NOT NULL CHECK (status IN ('pending', 'success', 'failed')),
  status_code INTEGER,
  response_body TEXT,
  error TEXT,
  attempt_count INTEGER NOT NULL DEFAULT 0,
  next_retry_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS webhook_deliveries_webhook_id_idx ON webhook_deliveries(webhook_id);
CREATE INDEX IF NOT EXISTS webhook_deliveries_status_idx ON webhook_deliveries(status);
CREATE INDEX IF NOT EXISTS webhook_deliveries_next_retry_at_idx ON webhook_deliveries(next_retry_at);

-- Create a trigger to update the updated_at timestamp
DROP TRIGGER IF EXISTS set_webhook_deliveries_updated_at ON webhook_deliveries;
CREATE TRIGGER set_webhook_deliveries_updated_at
BEFORE UPDATE ON webhook_deliveries
FOR EACH ROW
EXECUTE FUNCTION update_modified_column();

-- Create RLS policies for the webhooks table
ALTER TABLE webhooks ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their tenant's webhooks
CREATE POLICY view_tenant_webhooks ON webhooks
  FOR SELECT
  USING (
    tenant_id IN (
      SELECT tenant_id FROM user_tenants
      WHERE user_id = auth.uid()
    )
  );

-- Policy for users to insert webhooks for their tenant
CREATE POLICY insert_tenant_webhooks ON webhooks
  FOR INSERT
  WITH CHECK (
    tenant_id IN (
      SELECT tenant_id FROM user_tenants
      WHERE user_id = auth.uid()
    )
  );

-- Policy for users to update webhooks for their tenant
CREATE POLICY update_tenant_webhooks ON webhooks
  FOR UPDATE
  USING (
    tenant_id IN (
      SELECT tenant_id FROM user_tenants
      WHERE user_id = auth.uid()
    )
  );

-- Policy for users to delete webhooks for their tenant
CREATE POLICY delete_tenant_webhooks ON webhooks
  FOR DELETE
  USING (
    tenant_id IN (
      SELECT tenant_id FROM user_tenants
      WHERE user_id = auth.uid()
    )
  );

-- Create RLS policies for the webhook_deliveries table
ALTER TABLE webhook_deliveries ENABLE ROW LEVEL SECURITY;

-- Policy for users to view their tenant's webhook deliveries
CREATE POLICY view_tenant_webhook_deliveries ON webhook_deliveries
  FOR SELECT
  USING (
    webhook_id IN (
      SELECT id FROM webhooks
      WHERE tenant_id IN (
        SELECT tenant_id FROM user_tenants
        WHERE user_id = auth.uid()
      )
    )
  );

-- Create a function to trigger webhooks when a processing job is updated
CREATE OR REPLACE FUNCTION trigger_webhooks_on_job_update()
RETURNS TRIGGER AS $$
DECLARE
  webhook_record RECORD;
  delivery_id UUID;
  event_type TEXT;
BEGIN
  -- Only trigger webhooks when status changes to 'completed' or 'failed'
  IF (NEW.status = 'completed' OR NEW.status = 'failed') AND 
     (OLD.status != NEW.status) THEN
    
    -- Determine the event type
    event_type := 'job.' || NEW.status;
    
    -- Find all active webhooks for this tenant that are subscribed to this event
    FOR webhook_record IN 
      SELECT id, url, secret 
      FROM webhooks 
      WHERE tenant_id = NEW.tenant_id 
        AND is_active = true 
        AND event_type = ANY(events)
    LOOP
      -- Create a webhook delivery record
      INSERT INTO webhook_deliveries (
        webhook_id, 
        event_type, 
        payload, 
        status,
        attempt_count,
        next_retry_at
      ) VALUES (
        webhook_record.id,
        event_type,
        jsonb_build_object(
          'event', event_type,
          'job_id', NEW.id,
          'tenant_id', NEW.tenant_id,
          'status', NEW.status,
          'started_at', NEW.started_at,
          'completed_at', NEW.completed_at,
          'error', NEW.error,
          'metadata', NEW.metadata
        ),
        'pending',
        0,
        NOW()
      ) RETURNING id INTO delivery_id;
      
      -- Log the webhook delivery creation
      RAISE NOTICE 'Created webhook delivery % for webhook % with event %', 
        delivery_id, webhook_record.id, event_type;
    END LOOP;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply the trigger to the processing_jobs table
DROP TRIGGER IF EXISTS processing_jobs_webhook_trigger ON processing_jobs;
CREATE TRIGGER processing_jobs_webhook_trigger
AFTER UPDATE ON processing_jobs
FOR EACH ROW
EXECUTE FUNCTION trigger_webhooks_on_job_update();

-- Create a function to process pending webhook deliveries
CREATE OR REPLACE FUNCTION process_pending_webhook_deliveries(max_deliveries INTEGER DEFAULT 100)
RETURNS INTEGER AS $$
DECLARE
  delivery_record RECORD;
  processed_count INTEGER := 0;
BEGIN
  -- Find pending webhook deliveries that are due for processing
  FOR delivery_record IN 
    SELECT id, webhook_id, event_type, payload, attempt_count
    FROM webhook_deliveries
    WHERE status = 'pending'
      AND (next_retry_at IS NULL OR next_retry_at <= NOW())
    ORDER BY created_at ASC
    LIMIT max_deliveries
  LOOP
    -- Mark this delivery as being processed by updating next_retry_at
    -- This helps prevent the same delivery from being processed multiple times
    UPDATE webhook_deliveries
    SET next_retry_at = NOW() + INTERVAL '5 minutes'
    WHERE id = delivery_record.id;
    
    -- Log that we're processing this delivery
    RAISE NOTICE 'Processing webhook delivery % for webhook % with event %', 
      delivery_record.id, delivery_record.webhook_id, delivery_record.event_type;
    
    processed_count := processed_count + 1;
  END LOOP;
  
  RETURN processed_count;
END;
$$ LANGUAGE plpgsql;
