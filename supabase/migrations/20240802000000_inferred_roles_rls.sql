-- Migration to enable <PERSON><PERSON> on inferred_roles table and add appropriate policies
-- This ensures that users can only access inferred roles data for their own tenants

-- First, fix the tenant_id column type to be UUID for consistency with other tables
ALTER TABLE public.inferred_roles
  ALTER COLUMN tenant_id TYPE UUID USING tenant_id::UUID;

-- Enable RLS on the inferred_roles table
ALTER TABLE public.inferred_roles ENABLE ROW LEVEL SECURITY;

-- Add comment to explain the purpose of the table
COMMENT ON TABLE public.inferred_roles IS 'Stores inferred role assignments for users with confidence scores and metadata';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_inferred_roles_tenant_id
  ON public.inferred_roles (tenant_id);

CREATE INDEX IF NOT EXISTS idx_inferred_roles_user_id
  ON public.inferred_roles (user_id);

-- Create RLS policies for the inferred_roles table

-- Policy for users to view inferred roles for their tenant
CREATE POLICY "Users can view inferred roles for their tenants" ON public.inferred_roles
  FOR SELECT
  USING (
    tenant_id IN (
      SELECT tenant_id FROM public.user_tenants
      WHERE user_id = auth.uid()
    )
  );

-- Policy for users to view their own inferred roles
CREATE POLICY "Users can view their own inferred roles" ON public.inferred_roles
  FOR SELECT
  USING (
    user_id = auth.uid()
  );

-- Policy for service role to do anything with inferred_roles
CREATE POLICY "Service role can do anything with inferred_roles" ON public.inferred_roles
  USING (auth.role() = 'service_role')
  WITH CHECK (auth.role() = 'service_role');

-- Policy for users to insert inferred roles for their tenant (if they have appropriate permissions)
CREATE POLICY "Users can insert inferred roles for their tenant" ON public.inferred_roles
  FOR INSERT
  WITH CHECK (
    tenant_id IN (
      SELECT tenant_id FROM public.user_tenants
      WHERE user_id = auth.uid()
    )
    AND
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      JOIN public.role_permissions rp ON ur.role_id = rp.role_id
      JOIN public.permissions p ON rp.permission_id = p.id
      WHERE ur.user_id = auth.uid()
      AND ur.tenant_id = tenant_id
      AND p.name = 'manage_roles'
    )
  );

-- Policy for users to update inferred roles for their tenant (if they have appropriate permissions)
CREATE POLICY "Users can update inferred roles for their tenant" ON public.inferred_roles
  FOR UPDATE
  USING (
    tenant_id IN (
      SELECT tenant_id FROM public.user_tenants
      WHERE user_id = auth.uid()
    )
    AND
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      JOIN public.role_permissions rp ON ur.role_id = rp.role_id
      JOIN public.permissions p ON rp.permission_id = p.id
      WHERE ur.user_id = auth.uid()
      AND ur.tenant_id = tenant_id
      AND p.name = 'manage_roles'
    )
  );

-- Policy for users to delete inferred roles for their tenant (if they have appropriate permissions)
CREATE POLICY "Users can delete inferred roles for their tenant" ON public.inferred_roles
  FOR DELETE
  USING (
    tenant_id IN (
      SELECT tenant_id FROM public.user_tenants
      WHERE user_id = auth.uid()
    )
    AND
    EXISTS (
      SELECT 1 FROM public.user_roles ur
      JOIN public.role_permissions rp ON ur.role_id = rp.role_id
      JOIN public.permissions p ON rp.permission_id = p.id
      WHERE ur.user_id = auth.uid()
      AND ur.tenant_id = tenant_id
      AND p.name = 'manage_roles'
    )
  );
