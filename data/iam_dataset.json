[{"EmployeeID": "E001", "Name": "Employee_1", "Department": "Customer Support", "Role": "Technical Support", "AccessPermissions": ["Respond to Tickets", "Access Customer Profiles"], "ComplianceClassification": "Low"}, {"EmployeeID": "E002", "Name": "Employee_2", "Department": "Engineering", "Role": "QA Analyst", "AccessPermissions": ["Deploy Code", "Access Development Servers"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E003", "Name": "Employee_3", "Department": "Marketing", "Role": "Marketing Manager", "AccessPermissions": ["Edit Website Content", "Manage Ad Campaigns"], "ComplianceClassification": "High"}, {"EmployeeID": "E004", "Name": "Employee_4", "Department": "Engineering", "Role": "Software Engineer", "AccessPermissions": ["Deploy Code", "Access Development Servers"], "ComplianceClassification": "Low"}, {"EmployeeID": "E005", "Name": "Employee_5", "Department": "HR", "Role": "HR Manager", "AccessPermissions": ["View Employee Records", "Edit Payroll Data"], "ComplianceClassification": "High"}, {"EmployeeID": "E006", "Name": "Employee_6", "Department": "IT", "Role": "Network Engineer", "AccessPermissions": ["Manage User Accounts", "Monitor Network Security"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E007", "Name": "Employee_7", "Department": "IT", "Role": "Network Engineer", "AccessPermissions": ["Manage User Accounts", "Monitor Network Security"], "ComplianceClassification": "Low"}, {"EmployeeID": "E008", "Name": "Employee_8", "Department": "Marketing", "Role": "Content Strategist", "AccessPermissions": ["Edit Website Content", "Manage Ad Campaigns"], "ComplianceClassification": "High"}, {"EmployeeID": "E009", "Name": "Employee_9", "Department": "Finance", "Role": "Accountant", "AccessPermissions": ["Access Financial Reports", "Process Transactions"], "ComplianceClassification": "High"}, {"EmployeeID": "E010", "Name": "Employee_10", "Department": "HR", "Role": "Rec<PERSON>er", "AccessPermissions": ["View Employee Records", "Edit Payroll Data"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E011", "Name": "Employee_11", "Department": "Engineering", "Role": "DevOps Engineer", "AccessPermissions": ["Deploy Code", "Access Development Servers"], "ComplianceClassification": "High"}, {"EmployeeID": "E012", "Name": "Employee_12", "Department": "Sales", "Role": "Sales Manager", "AccessPermissions": ["View Customer Data", "Edit Sales Reports"], "ComplianceClassification": "High"}, {"EmployeeID": "E013", "Name": "Employee_13", "Department": "Legal", "Role": "Compliance Officer", "AccessPermissions": ["Access Compliance Documents", "Review Contracts"], "ComplianceClassification": "High"}, {"EmployeeID": "E014", "Name": "Employee_14", "Department": "Sales", "Role": "Sales Manager", "AccessPermissions": ["View Customer Data", "Edit Sales Reports"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E015", "Name": "Employee_15", "Department": "Finance", "Role": "Finance Manager", "AccessPermissions": ["Access Financial Reports", "Process Transactions"], "ComplianceClassification": "High"}, {"EmployeeID": "E016", "Name": "Employee_16", "Department": "Legal", "Role": "Compliance Officer", "AccessPermissions": ["Access Compliance Documents", "Review Contracts"], "ComplianceClassification": "High"}, {"EmployeeID": "E017", "Name": "Employee_17", "Department": "Customer Support", "Role": "Customer Service Representative", "AccessPermissions": ["Respond to Tickets", "Access Customer Profiles"], "ComplianceClassification": "High"}, {"EmployeeID": "E018", "Name": "Employee_18", "Department": "Legal", "Role": "Legal Counsel", "AccessPermissions": ["Access Compliance Documents", "Review Contracts"], "ComplianceClassification": "Low"}, {"EmployeeID": "E019", "Name": "Employee_19", "Department": "Finance", "Role": "Auditor", "AccessPermissions": ["Access Financial Reports", "Process Transactions"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E020", "Name": "Employee_20", "Department": "HR", "Role": "Payroll Specialist", "AccessPermissions": ["View Employee Records", "Edit Payroll Data"], "ComplianceClassification": "High"}, {"EmployeeID": "E021", "Name": "Employee_21", "Department": "Customer Support", "Role": "Technical Support", "AccessPermissions": ["Respond to Tickets", "Access Customer Profiles"], "ComplianceClassification": "High"}, {"EmployeeID": "E022", "Name": "Employee_22", "Department": "Finance", "Role": "Auditor", "AccessPermissions": ["Access Financial Reports", "Process Transactions"], "ComplianceClassification": "High"}, {"EmployeeID": "E023", "Name": "Employee_23", "Department": "Customer Support", "Role": "Support Manager", "AccessPermissions": ["Respond to Tickets", "Access Customer Profiles"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E024", "Name": "Employee_24", "Department": "Finance", "Role": "Finance Manager", "AccessPermissions": ["Access Financial Reports", "Process Transactions"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E025", "Name": "Employee_25", "Department": "HR", "Role": "Payroll Specialist", "AccessPermissions": ["View Employee Records", "Edit Payroll Data"], "ComplianceClassification": "High"}, {"EmployeeID": "E026", "Name": "Employee_26", "Department": "IT", "Role": "Network Engineer", "AccessPermissions": ["Manage User Accounts", "Monitor Network Security"], "ComplianceClassification": "High"}, {"EmployeeID": "E027", "Name": "Employee_27", "Department": "Engineering", "Role": "DevOps Engineer", "AccessPermissions": ["Deploy Code", "Access Development Servers"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E028", "Name": "Employee_28", "Department": "HR", "Role": "Payroll Specialist", "AccessPermissions": ["View Employee Records", "Edit Payroll Data"], "ComplianceClassification": "High"}, {"EmployeeID": "E029", "Name": "Employee_29", "Department": "Sales", "Role": "Account Executive", "AccessPermissions": ["View Customer Data", "Edit Sales Reports"], "ComplianceClassification": "Low"}, {"EmployeeID": "E030", "Name": "Employee_30", "Department": "Customer Support", "Role": "Customer Service Representative", "AccessPermissions": ["Respond to Tickets", "Access Customer Profiles"], "ComplianceClassification": "Low"}, {"EmployeeID": "E031", "Name": "Employee_31", "Department": "IT", "Role": "IT Administrator", "AccessPermissions": ["Manage User Accounts", "Monitor Network Security"], "ComplianceClassification": "Low"}, {"EmployeeID": "E032", "Name": "Employee_32", "Department": "Marketing", "Role": "Content Strategist", "AccessPermissions": ["Edit Website Content", "Manage Ad Campaigns"], "ComplianceClassification": "Low"}, {"EmployeeID": "E033", "Name": "Employee_33", "Department": "Legal", "Role": "Compliance Officer", "AccessPermissions": ["Access Compliance Documents", "Review Contracts"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E034", "Name": "Employee_34", "Department": "Legal", "Role": "Compliance Officer", "AccessPermissions": ["Access Compliance Documents", "Review Contracts"], "ComplianceClassification": "Low"}, {"EmployeeID": "E035", "Name": "Employee_35", "Department": "HR", "Role": "HR Manager", "AccessPermissions": ["View Employee Records", "Edit Payroll Data"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E036", "Name": "Employee_36", "Department": "Customer Support", "Role": "Customer Service Representative", "AccessPermissions": ["Respond to Tickets", "Access Customer Profiles"], "ComplianceClassification": "High"}, {"EmployeeID": "E037", "Name": "Employee_37", "Department": "Sales", "Role": "Sales Manager", "AccessPermissions": ["View Customer Data", "Edit Sales Reports"], "ComplianceClassification": "Low"}, {"EmployeeID": "E038", "Name": "Employee_38", "Department": "Finance", "Role": "Accountant", "AccessPermissions": ["Access Financial Reports", "Process Transactions"], "ComplianceClassification": "Low"}, {"EmployeeID": "E039", "Name": "Employee_39", "Department": "HR", "Role": "HR Manager", "AccessPermissions": ["View Employee Records", "Edit Payroll Data"], "ComplianceClassification": "Low"}, {"EmployeeID": "E040", "Name": "Employee_40", "Department": "HR", "Role": "Rec<PERSON>er", "AccessPermissions": ["View Employee Records", "Edit Payroll Data"], "ComplianceClassification": "High"}, {"EmployeeID": "E041", "Name": "Employee_41", "Department": "Engineering", "Role": "DevOps Engineer", "AccessPermissions": ["Deploy Code", "Access Development Servers"], "ComplianceClassification": "High"}, {"EmployeeID": "E042", "Name": "Employee_42", "Department": "IT", "Role": "IT Administrator", "AccessPermissions": ["Manage User Accounts", "Monitor Network Security"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E043", "Name": "Employee_43", "Department": "Finance", "Role": "Finance Manager", "AccessPermissions": ["Access Financial Reports", "Process Transactions"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E044", "Name": "Employee_44", "Department": "IT", "Role": "Security Analyst", "AccessPermissions": ["Manage User Accounts", "Monitor Network Security"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E045", "Name": "Employee_45", "Department": "Engineering", "Role": "DevOps Engineer", "AccessPermissions": ["Deploy Code", "Access Development Servers"], "ComplianceClassification": "High"}, {"EmployeeID": "E046", "Name": "Employee_46", "Department": "Finance", "Role": "Accountant", "AccessPermissions": ["Access Financial Reports", "Process Transactions"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E047", "Name": "Employee_47", "Department": "Legal", "Role": "Contract Specialist", "AccessPermissions": ["Access Compliance Documents", "Review Contracts"], "ComplianceClassification": "Low"}, {"EmployeeID": "E048", "Name": "Employee_48", "Department": "IT", "Role": "Security Analyst", "AccessPermissions": ["Manage User Accounts", "Monitor Network Security"], "ComplianceClassification": "Low"}, {"EmployeeID": "E049", "Name": "Employee_49", "Department": "HR", "Role": "Payroll Specialist", "AccessPermissions": ["View Employee Records", "Edit Payroll Data"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E050", "Name": "Employee_50", "Department": "Customer Support", "Role": "Customer Service Representative", "AccessPermissions": ["Respond to Tickets", "Access Customer Profiles"], "ComplianceClassification": "Low"}, {"EmployeeID": "E051", "Name": "Employee_51", "Department": "Finance", "Role": "Finance Manager", "AccessPermissions": ["Access Financial Reports", "Process Transactions"], "ComplianceClassification": "High"}, {"EmployeeID": "E052", "Name": "Employee_52", "Department": "Sales", "Role": "Account Executive", "AccessPermissions": ["View Customer Data", "Edit Sales Reports"], "ComplianceClassification": "High"}, {"EmployeeID": "E053", "Name": "Employee_53", "Department": "IT", "Role": "Network Engineer", "AccessPermissions": ["Manage User Accounts", "Monitor Network Security"], "ComplianceClassification": "Low"}, {"EmployeeID": "E054", "Name": "Employee_54", "Department": "Engineering", "Role": "Software Engineer", "AccessPermissions": ["Deploy Code", "Access Development Servers"], "ComplianceClassification": "Low"}, {"EmployeeID": "E055", "Name": "Employee_55", "Department": "IT", "Role": "Network Engineer", "AccessPermissions": ["Manage User Accounts", "Monitor Network Security"], "ComplianceClassification": "High"}, {"EmployeeID": "E056", "Name": "Employee_56", "Department": "Marketing", "Role": "SEO Specialist", "AccessPermissions": ["Edit Website Content", "Manage Ad Campaigns"], "ComplianceClassification": "High"}, {"EmployeeID": "E057", "Name": "Employee_57", "Department": "Finance", "Role": "Auditor", "AccessPermissions": ["Access Financial Reports", "Process Transactions"], "ComplianceClassification": "High"}, {"EmployeeID": "E058", "Name": "Employee_58", "Department": "Engineering", "Role": "QA Analyst", "AccessPermissions": ["Deploy Code", "Access Development Servers"], "ComplianceClassification": "Low"}, {"EmployeeID": "E059", "Name": "Employee_59", "Department": "Customer Support", "Role": "Technical Support", "AccessPermissions": ["Respond to Tickets", "Access Customer Profiles"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E060", "Name": "Employee_60", "Department": "HR", "Role": "Rec<PERSON>er", "AccessPermissions": ["View Employee Records", "Edit Payroll Data"], "ComplianceClassification": "High"}, {"EmployeeID": "E061", "Name": "Employee_61", "Department": "Legal", "Role": "Compliance Officer", "AccessPermissions": ["Access Compliance Documents", "Review Contracts"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E062", "Name": "Employee_62", "Department": "HR", "Role": "HR Manager", "AccessPermissions": ["View Employee Records", "Edit Payroll Data"], "ComplianceClassification": "Low"}, {"EmployeeID": "E063", "Name": "Employee_63", "Department": "Customer Support", "Role": "Technical Support", "AccessPermissions": ["Respond to Tickets", "Access Customer Profiles"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E064", "Name": "Employee_64", "Department": "Legal", "Role": "Compliance Officer", "AccessPermissions": ["Access Compliance Documents", "Review Contracts"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E065", "Name": "Employee_65", "Department": "Sales", "Role": "Sales Manager", "AccessPermissions": ["View Customer Data", "Edit Sales Reports"], "ComplianceClassification": "Low"}, {"EmployeeID": "E066", "Name": "Employee_66", "Department": "Finance", "Role": "Accountant", "AccessPermissions": ["Access Financial Reports", "Process Transactions"], "ComplianceClassification": "Low"}, {"EmployeeID": "E067", "Name": "Employee_67", "Department": "Customer Support", "Role": "Support Manager", "AccessPermissions": ["Respond to Tickets", "Access Customer Profiles"], "ComplianceClassification": "High"}, {"EmployeeID": "E068", "Name": "Employee_68", "Department": "Engineering", "Role": "Software Engineer", "AccessPermissions": ["Deploy Code", "Access Development Servers"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E069", "Name": "Employee_69", "Department": "Sales", "Role": "Account Executive", "AccessPermissions": ["View Customer Data", "Edit Sales Reports"], "ComplianceClassification": "High"}, {"EmployeeID": "E070", "Name": "Employee_70", "Department": "IT", "Role": "Security Analyst", "AccessPermissions": ["Manage User Accounts", "Monitor Network Security"], "ComplianceClassification": "Low"}, {"EmployeeID": "E071", "Name": "Employee_71", "Department": "Customer Support", "Role": "Technical Support", "AccessPermissions": ["Respond to Tickets", "Access Customer Profiles"], "ComplianceClassification": "High"}, {"EmployeeID": "E072", "Name": "Employee_72", "Department": "HR", "Role": "Rec<PERSON>er", "AccessPermissions": ["View Employee Records", "Edit Payroll Data"], "ComplianceClassification": "Low"}, {"EmployeeID": "E073", "Name": "Employee_73", "Department": "Legal", "Role": "Contract Specialist", "AccessPermissions": ["Access Compliance Documents", "Review Contracts"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E074", "Name": "Employee_74", "Department": "Sales", "Role": "Sales Manager", "AccessPermissions": ["View Customer Data", "Edit Sales Reports"], "ComplianceClassification": "Low"}, {"EmployeeID": "E075", "Name": "Employee_75", "Department": "Engineering", "Role": "QA Analyst", "AccessPermissions": ["Deploy Code", "Access Development Servers"], "ComplianceClassification": "High"}, {"EmployeeID": "E076", "Name": "Employee_76", "Department": "Marketing", "Role": "SEO Specialist", "AccessPermissions": ["Edit Website Content", "Manage Ad Campaigns"], "ComplianceClassification": "Low"}, {"EmployeeID": "E077", "Name": "Employee_77", "Department": "Customer Support", "Role": "Support Manager", "AccessPermissions": ["Respond to Tickets", "Access Customer Profiles"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E078", "Name": "Employee_78", "Department": "Customer Support", "Role": "Support Manager", "AccessPermissions": ["Respond to Tickets", "Access Customer Profiles"], "ComplianceClassification": "Low"}, {"EmployeeID": "E079", "Name": "Employee_79", "Department": "Marketing", "Role": "Marketing Manager", "AccessPermissions": ["Edit Website Content", "Manage Ad Campaigns"], "ComplianceClassification": "Low"}, {"EmployeeID": "E080", "Name": "Employee_80", "Department": "Marketing", "Role": "SEO Specialist", "AccessPermissions": ["Edit Website Content", "Manage Ad Campaigns"], "ComplianceClassification": "Low"}, {"EmployeeID": "E081", "Name": "Employee_81", "Department": "HR", "Role": "HR Manager", "AccessPermissions": ["View Employee Records", "Edit Payroll Data"], "ComplianceClassification": "High"}, {"EmployeeID": "E082", "Name": "Employee_82", "Department": "IT", "Role": "Network Engineer", "AccessPermissions": ["Manage User Accounts", "Monitor Network Security"], "ComplianceClassification": "Medium"}, {"EmployeeID": "E083", "Name": "Employee_83", "Department": "Sales", "Role": "Account Executive", "AccessPermissions": ["View Customer Data", "Edit Sales Reports"], "ComplianceClassification": "Low"}, {"EmployeeID": "E084", "Name": "Employee_84", "Department": "Engineering", "Role": "QA Analyst", "AccessPermissions": ["Deploy Code", "Access Development Servers"], "ComplianceClassification": "Low"}, {"EmployeeID": "E085", "Name": "Employee_85", "Department": "IT", "Role": "IT Administrator", "AccessPermissions": ["Manage User Accounts", "Monitor Network Security"], "ComplianceClassification": "Low"}, {"EmployeeID": "E086", "Name": "Employee_86", "Department": "Legal", "Role": "Contract Specialist", "AccessPermissions": ["Access Compliance Documents", "Review Contracts"], "ComplianceClassification": "High"}, {"EmployeeID": "E087", "Name": "Employee_87", "Department": "Marketing", "Role": "Content Strategist", "AccessPermissions": ["Edit Website Content", "Manage Ad Campaigns"], "ComplianceClassification": "Low"}, {"EmployeeID": "E088", "Name": "Employee_88", "Department": "Sales", "Role": "Sales Representative", "AccessPermissions": ["View Customer Data", "Edit Sales Reports"], "ComplianceClassification": "High"}, {"EmployeeID": "E089", "Name": "Employee_89", "Department": "IT", "Role": "Security Analyst", "AccessPermissions": ["Manage User Accounts", "Monitor Network Security"], "ComplianceClassification": "High"}, {"EmployeeID": "E090", "Name": "Employee_90", "Department": "Sales", "Role": "Sales Representative", "AccessPermissions": ["View Customer Data", "Edit Sales Reports"], "ComplianceClassification": "Low"}, {"EmployeeID": "E091", "Name": "Employee_91", "Department": "Customer Support", "Role": "Support Manager", "AccessPermissions": ["Respond to Tickets", "Access Customer Profiles"], "ComplianceClassification": "High"}, {"EmployeeID": "E092", "Name": "Employee_92", "Department": "Marketing", "Role": "Marketing Manager", "AccessPermissions": ["Edit Website Content", "Manage Ad Campaigns"], "ComplianceClassification": "Low"}, {"EmployeeID": "E093", "Name": "Employee_93", "Department": "Customer Support", "Role": "Support Manager", "AccessPermissions": ["Respond to Tickets", "Access Customer Profiles"], "ComplianceClassification": "High"}, {"EmployeeID": "E094", "Name": "Employee_94", "Department": "IT", "Role": "IT Administrator", "AccessPermissions": ["Manage User Accounts", "Monitor Network Security"], "ComplianceClassification": "Low"}, {"EmployeeID": "E095", "Name": "Employee_95", "Department": "Legal", "Role": "Contract Specialist", "AccessPermissions": ["Access Compliance Documents", "Review Contracts"], "ComplianceClassification": "High"}, {"EmployeeID": "E096", "Name": "Employee_96", "Department": "Engineering", "Role": "Software Engineer", "AccessPermissions": ["Deploy Code", "Access Development Servers"], "ComplianceClassification": "Low"}, {"EmployeeID": "E097", "Name": "Employee_97", "Department": "Engineering", "Role": "DevOps Engineer", "AccessPermissions": ["Deploy Code", "Access Development Servers"], "ComplianceClassification": "High"}, {"EmployeeID": "E098", "Name": "Employee_98", "Department": "HR", "Role": "Payroll Specialist", "AccessPermissions": ["View Employee Records", "Edit Payroll Data"], "ComplianceClassification": "Low"}, {"EmployeeID": "E099", "Name": "Employee_99", "Department": "Finance", "Role": "Auditor", "AccessPermissions": ["Access Financial Reports", "Process Transactions"], "ComplianceClassification": "Low"}, {"EmployeeID": "E100", "Name": "Employee_100", "Department": "Customer Support", "Role": "Customer Service Representative", "AccessPermissions": ["Respond to Tickets", "Access Customer Profiles"], "ComplianceClassification": "Low"}]