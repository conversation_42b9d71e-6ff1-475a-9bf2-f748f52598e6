# Define a list of office locations (city, state, ZIP, area code)
$officeLocations = @(
    @{
        City = "Chicago"
        State = "IL"
        ZipCode = "60601"
        AreaCode = "312"
        StreetAddress = "123 Main St"
        Country = "United States"
    },
    @{
        City = "New York"
        State = "NY"
        ZipCode = "10001"
        AreaCode = "212"
        StreetAddress = "456 Park Ave"
        Country = "United States"
    },
    @{
        City = "San Francisco"
        State = "CA"
        ZipCode = "94105"
        AreaCode = "415"
        StreetAddress = "789 Market St"
        Country = "United States"
    },
    @{
        City = "Austin"
        State = "TX"
        ZipCode = "78701"
        AreaCode = "512"
        StreetAddress = "321 Congress Ave"
        Country = "United States"
    },
    @{
        City = "Los Angeles"
        State = "CA"
        ZipCode = "90001"
        AreaCode = "213"
        StreetAddress = "789 Market St"
        Country = "United States"
    },
    @{
        City = "Seattle"
        State = "WA"
        ZipCode = "98101"
        AreaCode = "206"
        StreetAddress = "654 Pine St"
        Country = "United States"
    }
)

# Function to generate a random phone number
function Get-RandomPhoneNumber {
    param (
        [string]$AreaCode
    )
    $randomPart1 = Get-Random -Minimum 100 -Maximum 999
    $randomPart2 = Get-Random -Minimum 1000 -Maximum 9999
    return "($AreaCode)$randomPart1-$randomPart2"
}

# Read the input CSV
$inputCsv = Import-Csv -Path "users_for_entra_import.csv"

# Transform the data
$outputData = $inputCsv | ForEach-Object {
    # Randomly assign an office location
    $office = $officeLocations | Get-Random

    # Generate random phone number based on office area code
    $phoneNumber = Get-RandomPhoneNumber -AreaCode $office.AreaCode

    # Create updated user object
    [PSCustomObject]@{
        "Name [displayName]" = $_."Name [displayName]"
        "User name [userPrincipalName]" = $_."User name [userPrincipalName]"
        "Initial password [passwordProfile]" = $_."Initial password [passwordProfile]"
        "Block sign in (Yes/No) [accountEnabled]" = $_."Block sign in (Yes/No) [accountEnabled]"
        "First name [givenName]" = $_."First name [givenName]"
        "Last name [surname]" = $_."Last name [surname]"
        "Job title [jobTitle]" = $_."Job title [jobTitle]"
        "Department [department]" = $_."Department [department]"
        "Usage location [usageLocation]" = $_."Usage location [usageLocation]"
        "Street address [streetAddress]" = $office.StreetAddress
        "State or province [state]" = $office.State
        "Country or region [country]" = $office.Country
        "Office [physicalDeliveryOfficeName]" = $_."Office [physicalDeliveryOfficeName]"
        "City [city]" = $office.City
        "ZIP or postal code [postalCode]" = $office.ZipCode
        "Office phone [telephoneNumber]" = $phoneNumber
        "Mobile phone [mobile]" = $_."Mobile phone [mobile]"
    }
}

# Write the output CSV with the version header
"version:v1.0" | Out-File -FilePath "users_for_entra_import_updated.csv" -Encoding utf8
$outputData | Export-Csv -Path "users_for_entra_import_updated.csv" -NoTypeInformation -Encoding utf8

Write-Host "Updated CSV saved as 'users_for_entra_import_updated.csv'"