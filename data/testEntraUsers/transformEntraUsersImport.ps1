# Read the input CSV
$inputCsv = Import-Csv -Path "users_for_import.csv"

# Define the default password
$defaultPassword = -join ((65..90) + (97..122) + (48..57) | Get-Random -Count 12 | ForEach-Object {[char]$_}) + "!"

# Define the template headers exactly as specified in UserCreateTemplate.csv
$templateHeaders = @(
    "Name [displayName]",
    "User name [userPrincipalName]",
    "Initial password [passwordProfile]",
    "Block sign in (Yes/No) [accountEnabled]",
    "First name [givenName]",
    "Last name [surname]",
    "Job title [jobTitle]",
    "Department [department]",
    "Usage location [usageLocation]",
    "Street address [streetAddress]",
    "State or province [state]",
    "Country or region [country]",
    "Office [physicalDeliveryOfficeName]",
    "City [city]",
    "ZIP or postal code [postalCode]",
    "Office phone [telephoneNumber]",
    "Mobile phone [mobile]"
)

# Transform the data
$outputData = $inputCsv | ForEach-Object {
    [PSCustomObject]@{
        "Name [displayName]" = $_.DisplayName
        "User name [userPrincipalName]" = $_.UserPrincipalName
        "Initial password [passwordProfile]" = $defaultPassword
        "Block sign in (Yes/No) [accountEnabled]" = "No"
        "First name [givenName]" = $_.FirstName
        "Last name [surname]" = $_.LastName
        "Job title [jobTitle]" = $_.JobTitle
        "Department [department]" = $_.Department
        "Usage location [usageLocation]" = $_.UsageLocation
        "Street address [streetAddress]" = ""
        "State or province [state]" = ""
        "Country or region [country]" = ""
        "Office [physicalDeliveryOfficeName]" = ""
        "City [city]" = ""
        "ZIP or postal code [postalCode]" = ""
        "Office phone [telephoneNumber]" = ""
        "Mobile phone [mobile]" = ""
    }
}

# Write the output CSV with the version header
"version:v1.0" | Out-File -FilePath "users_for_entra_import.csv" -Encoding utf8
$outputData | Export-Csv -Path "users_for_entra_import.csv" -NoTypeInformation -Encoding utf8

Write-Host "Transformed CSV saved as 'users_for_entra_import.csv'"